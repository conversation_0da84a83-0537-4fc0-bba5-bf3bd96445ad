# Keep the file list alphabetically sorted, makes diffs and merges easier
#
bzip2
cmake
curl
ed
g++
git
git-lfs
gpg
graphicsmagick
libcurl4-openssl-dev
libffi-dev
libgpgme-dev
libicu-dev
libimage-exiftool-perl
libkrb5-dev
libpcre2-dev
libpq-dev
libreadline-dev
libsqlite3-dev
libssl-dev
libyaml-dev
lsb-core
lsof
meson
net-tools
nginx
ninja-build
openssh-server
pkg-config
psmisc
python3-docutils
redis-server
rsync
runit
sudo
tzdata
unzip
uuid-dev
wget
xz-utils
yamllint
zlib1g-dev
