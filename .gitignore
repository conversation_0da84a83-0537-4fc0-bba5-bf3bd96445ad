# Please keep this file alphabetically sorted, thanks!
#
/repositories
*.crt
*.key
*.pem
*.p12
*.csr
*.swp
.DS_Store
.bundle/
.cache/
.gdkrc.custom
.idea
.nova
.runner_system_id
.yardoc
/*.log
/.backups/
/.env
/.gettext
/.gitaly-ruby-bundle
/.gitlab-bundle
/.gitlab-lefthook
/.gitlab-npm
/.gitlab-shell-bundle
/.gitlab-translations
/.gitlab-ui-yarn
/.gitlab-yarn
/.node-version
/.ruby-gemset
/.ssh/
/.vagrant/
/.vscode/
/Procfile
/artifacts/
/bootstrap-rails.log
/builds
/gitlab-cells/
/charts-gitlab
/clickhouse
/consul/
/container-registry
/coverage
/db_backup
/dev/
/doc-site/public/
/doc-site/.hugo_build.lock
/elasticsearch*
/env.runit
/gdk-ce/**
/gdk-config.mk
/gdk-ee/**
/gdk.yml
/gem/*.gem
/gettext.log
/gitaly/
# /gitlab-ai-gateway/
/gitlab-development-kit/**
/gitlab-docs
/gitlab-docs-hugo
/docs-gitlab-com
/gitlab-elasticsearch-indexer
/gitlab-http-router/
/gitlab-topology-service/
/gitlab-k8s-agent-config.yml
/gitlab-k8s-agent/
/gitlab-kas-websocket-token-secret
/gitlab-**********************workflow-data-encryption-secret
/gitlab-last-verified-sha.json
/gitlab-operator
/gitlab-pages
/gitlab-pages-secret
/gitlab-runner
/gitlab-runner-config.toml
/gitlab-shell
/gitlab-shell/
/gitlab-spamcheck
/gitlab-ui
/gitlab-workhorse
/gitlab-zoekt-indexer
/gitlab-observability-backend
/gitlab/
/gitlab_pages_port
/google_oauth_client_id
/google_oauth_client_secret
/grafana/
/host
/hostname
/https_enabled
/influxdb/
/jaeger-artifacts/
/jaeger/
/kerberos/http.keytab
/lefthook-local.yml
/log/
/mattermost
/minio
/nginx/client_body_temp
/nginx/conf/nginx.conf
/nginx/fastcgi_temp
/nginx/logs
/nginx/proxy_temp
/nginx/scgi_temp
/nginx/tmp
/nginx/uwsgi_temp
/object_store_enabled
/object_store_port
/omnibus-gitlab
/openbao
/openbao-internal
/openssh/*.pub
/openssh/*_key
/openssh/sshd_config
/pgbouncers/
/pgvector/
/port
/postgresql-geo/
/postgresql-primary
/postgresql-replica-2/.s.*
/postgresql-replica-2/data
/postgresql-replica/.s.*
/postgresql-replica/data
/postgresql/.s.*
/postgresql/data*
/postgresql_geo_port
/postgresql_port
/prometheus/prometheus.yml
/public
/redis-cluster/*.conf
/redis/*.conf
/redis/dump.rdb
/registry/config.yml
/registry/storage/
/registry_enabled
/registry_external_port
/registry_host
/registry_port
/relative_url_root
/repositories/
/repository_storages/
/services/
/sitespeed-result/
/siphon/
/nats/
/snowplow
/spec/examples.txt
/sv/
/webpack_port
/zoekt-data/
/zoekt/
/sandbox/
/duo-workflow-executor/
/duo-workflow-service/
Brewfile.lock.json
haproxy/haproxy.cfg
node_modules/
/.combined-tool-versions
