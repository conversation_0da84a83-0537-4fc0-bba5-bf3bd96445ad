.build-job:
  stage: build
  variables:
    MISE_HTTP_TIMEOUT: "60s"
    MISE_FETCH_REMOTE_VERSIONS_TIMEOUT: "60s"
  extends:
    - .docker:build-docker-in-docker
  tags:
    - saas-linux-large-amd64

build-docs:
  stage: build
  extends:
    - .rules:docs-code-changes
  image: ${GITLAB_DEPENDENCY_PROXY}hugomods/hugo:node-lts-${HUGO_VERSION}
  script:
    - apk add git
    # Build the documentation site
    - cd doc-site
    - hugo --minify --contentDir ../doc --destination ../public --baseURL "${CI_PAGES_URL}"
  artifacts:
    paths:
      - public/*
    expire_in: 1 week
  variables:
    GIT_SUBMODULE_STRATEGY: recursive
    GIT_SUBMODULE_PATHS: doc-site/themes/hextra
    GIT_SUBMODULE_DEPTH: 1

release-image:
  extends:
    - .build-job
  script:
    - ./support/docker ci-build-if-necessary
  rules:
    - !reference [.rules:code-changes, rules]
    # Re-tag latest image with git sha on no-op changes
    # This ensures that each commit sha in default branch has a corresponding image tagged with same sha
    - if: '$CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"'
      variables:
        RETAG_LATEST_RELEASE: "true"

build-gitpod-workspace-image:
  extends:
    - .build-job
    - .rules:gitpod-code-changes
  script:
    - ./support/docker-build ./support/gitpod "$GITPOD_WORKSPACE_IMAGE:$CI_COMMIT_REF_SLUG"

build-gitlab-remote-workspace-base-image:
  extends:
    - .build-job
    - .rules:gitlab-remote-workspace-base-changes
  before_script:
    - !reference [ .build-job, before_script ]
    - cp packages_ubuntu.txt .mise-version support/gitlab-remote-development/
  script:
    - DEPENDENCY_FILES=".mise-version .tool-versions mise.toml packages_ubuntu.txt support/bootstrap support/bootstrap-common.sh support/docker-build support/gitlab-remote-development/Dockerfile.base"
    - export DEPENDENCY_HASH=$(cat $DEPENDENCY_FILES | sha256sum | awk '{ print $1 }')
    - ./support/docker-build -f Dockerfile.base ./support/gitlab-remote-development "$GITLAB_REMOTE_WORKSPACE_BASE_IMAGE:$CI_COMMIT_REF_SLUG"

build-gitlab-remote-workspace-image:
  extends:
    - .build-job
    - .rules:gitlab-remote-code-changes
  needs:
    - job: build-gitlab-remote-workspace-base-image
      optional: true
  script:
    - DEPENDENCY_FILES=".mise-version .tool-versions mise.toml packages_ubuntu.txt support/bootstrap support/bootstrap-common.sh support/docker-build $(find support/gitlab-remote-development -type f | xargs)"
    - export DEPENDENCY_HASH=$(cat $DEPENDENCY_FILES | sha256sum | awk '{ print $1 }')
    - |
      if docker manifest inspect "${CI_REGISTRY_IMAGE}/$GITLAB_REMOTE_WORKSPACE_BASE_IMAGE:$CI_COMMIT_REF_SLUG" >/dev/null 2>&1; then
        BASE_IMAGE="${CI_REGISTRY_IMAGE}/$GITLAB_REMOTE_WORKSPACE_BASE_IMAGE:$CI_COMMIT_REF_SLUG"
      else
        BASE_IMAGE="${CI_REGISTRY_IMAGE}/$GITLAB_REMOTE_WORKSPACE_BASE_IMAGE:main"
      fi
    - ./support/docker-build ./support/gitlab-remote-development "$GITLAB_REMOTE_WORKSPACE_IMAGE:$CI_COMMIT_REF_SLUG" --build-arg from_image="$BASE_IMAGE"

build-gdk-in-a-box-image:
  extends:
    - .build-job
    - .rules:gitlab-gdk-in-a-box-code-changes
  script:
    - '[ "$CI_COMMIT_REF_NAME" = "$CI_DEFAULT_BRANCH" ] && export CACHE_FROM_ARGS="--no-cache"'
    - ./support/docker-build ./support/gdk-in-a-box/container "$GITLAB_GDK_IN_A_BOX_IMAGE:$CI_COMMIT_REF_SLUG-$PLATFORM"
  tags:
    - saas-linux-large-$PLATFORM
  parallel:
    matrix:
      - PLATFORM: [amd64, arm64]

build-gdk-in-a-box-single-image:
  extends:
    - .build-job
    - .rules:gitlab-gdk-in-a-box-code-changes
  needs:
    - build-gdk-in-a-box-image
  variables:
    DOCKER_IMAGE_NAME: $GITLAB_GDK_IN_A_BOX_IMAGE:$CI_COMMIT_REF_SLUG
  script:
    - ./support/gdk-in-a-box/container/create-manifest.sh $DOCKER_IMAGE_NAME
