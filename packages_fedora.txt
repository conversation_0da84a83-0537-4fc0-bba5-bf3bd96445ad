# Keep the file list alphabetically sorted, makes diffs and merges easier
#
GraphicsMagick
bzip2
cmake
ed
gcc
gcc-c++
git
git-lfs
go
gpgme-devel
icu
krb5-devel
libcurl-devel
libffi-devel
libicu-devel
libuuid-devel
libyaml-devel
lsof
meson
nginx
ninja-build
openssl
openssl-devel
openssl-libs
perl-core
perl-Digest-SHA
perl-Image-ExifTool
python3-docutils
readline-devel
redis
rpm-build
rsync
sqlite-devel
unzip
uuid-devel
wget
which
xz
yamllint
zlib-devel
