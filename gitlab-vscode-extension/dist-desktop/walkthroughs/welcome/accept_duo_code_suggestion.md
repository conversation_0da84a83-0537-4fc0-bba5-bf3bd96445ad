<figure align="center">
    <div>
        <p align="left">GitLab Duo Code Suggestions</p>
        <img src="2a_statusbar_codesuggestions_enabled.png" alt="Mouse hovering GitLab tanuki icon in status bar. Tool tip describes extension's current state." />
        <figcaption>At the bottom of VS Code, in the status bar, confirm GitLab Duo Code Suggestions is enabled and your file's language is supported. To enable unsupported languages, add the <a href="https://code.visualstudio.com/docs/languages/identifiers#_known-language-identifiers">language identifier</a> to the extension's <a href="https://docs.gitlab.com/user/project/repository/code_suggestions/supported_extensions/#add-support-for-more-languages
">additional language list</a> in settings.</figcaption>
    </div>
</figure>
<figure align="center">
    <div>
        <p align="left" >GitLab Duo Code Suggestions</p>
        <img src="2b_codesuggestion_accept.png" alt="Mouse hovering over a code suggestion in the editor. VS Code displays a toolbar with the option to toggle alternate suggestions." />
        <figcaption>Autocomplete suggestions appear as you write code. Hover to see alternatives.</figcaption>
    </div>
</figure>
