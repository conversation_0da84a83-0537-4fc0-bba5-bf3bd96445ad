"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const vscode = __importStar(require("vscode"));
const create_fake_partial_1 = require("../common/test_utils/create_fake_partial");
const snowplow_options_1 = require("../common/snowplow/snowplow_options");
const gitlab_telemetry_environment_browser_1 = require("./gitlab_telemetry_environment_browser");
describe('GitLabTelemetryEnvironmentBrowser', () => {
    let subject;
    let webIDEExtensionMock;
    beforeEach(async () => {
        webIDEExtensionMock = (0, create_fake_partial_1.createFakePartial)({
            isTelemetryEnabled: jest.fn(),
        });
        jest.mocked(vscode.extensions.getExtension).mockReturnValueOnce((0, create_fake_partial_1.createFakePartial)({
            exports: webIDEExtensionMock,
        }));
        subject = new gitlab_telemetry_environment_browser_1.GitLabTelemetryEnvironmentBrowser(webIDEExtensionMock);
    });
    describe('isTelemetryEnabled', () => {
        it.each `
      telemetryStatus
      ${true}
      ${false}
    `('returns telemetry $telemetryStatus when the Web IDE extension isTelemetryEnabled returns $telemetryStatus', async ({ telemetryStatus }) => {
            jest.mocked(webIDEExtensionMock.isTelemetryEnabled).mockReturnValueOnce(telemetryStatus);
            expect(subject.isTelemetryEnabled()).toBe(telemetryStatus);
        });
    });
    it('buildIdeExtensionContext build webIDE specific context', () => {
        expect(subject.buildIdeExtensionContext('1.0.0')).toStrictEqual({
            schema: snowplow_options_1.IDE_EXTENSION_VERSION_SCHEMA_URL,
            data: {
                ide_name: 'GitLab Web IDE',
                ide_vendor: 'GitLab Inc.',
                ide_version: 'vscode-test-version-0.0',
                extension_name: 'GitLab Workflow',
                extension_version: '1.0.0',
            },
        });
    });
});
//# sourceMappingURL=gitlab_telemetry_environment_browser.test.js.map