"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = void 0;
const vscode = __importStar(require("vscode"));
const log_1 = require("../common/log");
const main_1 = require("../common/main");
const local_feature_flag_service_1 = require("../common/feature_flags/local_feature_flag_service");
const language_server_manager_1 = require("../common/language_server/language_server_manager");
const code_suggestions_1 = require("../common/code_suggestions/code_suggestions");
const webview_1 = require("../common/webview");
const web_ide_1 = require("../common/platform/web_ide");
const migrations_1 = require("../common/utils/extension_configuration_migrations/migrations");
const ai_context_manager_web_ide_1 = require("../common/chat/ai_context_manager_web_ide");
const language_server_feature_state_provider_1 = require("../common/language_server/language_server_feature_state_provider");
const diagnostics_service_1 = require("../common/diagnostics/diagnostics_service");
const extension_state_service_1 = require("../common/state/extension_state_service");
const version_state_provider_1 = require("../common/state/version_state_provider");
const version_diagnostics_renderer_1 = require("../common/diagnostics/version_diagnostics/version_diagnostics_renderer");
const gitlab_instance_version_provider_1 = require("../common/state/gitlab_instance_version_provider");
const feature_state_diagnostics_renderer_1 = require("../common/diagnostics/feature_state_diagnostics/feature_state_diagnostics_renderer");
const settings_state_provider_1 = require("../common/state/settings_state_provider");
const extension_configuration_service_1 = require("../common/utils/extension_configuration_service");
const browser_language_client_factory_1 = require("./language_server/browser_language_client_factory");
const dependency_container_browser_1 = require("./dependency_container_browser");
const settings_diagnostics_renderer_1 = require("./diagnostics/settings_diagnostics_renderer");
const activate = async (context) => {
    const webIdeExtension = vscode.extensions.getExtension(web_ide_1.WEB_IDE_EXTENSION_ID)?.exports;
    if (!webIdeExtension) {
        throw new Error(`Failed to load extension export from ${web_ide_1.WEB_IDE_EXTENSION_ID}.`);
    }
    const outputChannel = vscode.window.createOutputChannel('GitLab Workflow');
    (0, log_1.initializeLogging)(line => outputChannel.appendLine(line));
    await (0, migrations_1.runExtensionConfigurationMigrations)();
    const dependencyContainer = await (0, dependency_container_browser_1.createDependencyContainer)(webIdeExtension);
    // browser always has account linked and repo opened.
    await vscode.commands.executeCommand('setContext', 'gitlab:noAccount', false);
    await vscode.commands.executeCommand('setContext', 'gitlab:validState', true);
    // TODO: integrate language server into web IDE duo chat
    const aiContextManager = new ai_context_manager_web_ide_1.AIContextManagerWebIde();
    const languageServerFeatureStateProvider = new language_server_feature_state_provider_1.LanguageServerFeatureStateProvider();
    const extensionStateService = new extension_state_service_1.DefaultExtensionStateService();
    const diagnosticsService = new diagnostics_service_1.DefaultDiagnosticsService(extensionStateService);
    let languageServerManager;
    if ((0, local_feature_flag_service_1.getLocalFeatureFlagService)().isEnabled(local_feature_flag_service_1.FeatureFlag.LanguageServerWebIDE)) {
        const webviewMessageRegistry = new webview_1.WebviewMessageRegistry();
        languageServerManager = new language_server_manager_1.LanguageServerManager(context, browser_language_client_factory_1.browserLanguageClientFactory, dependencyContainer, webviewMessageRegistry, languageServerFeatureStateProvider);
        await languageServerManager.startLanguageServer();
        context.subscriptions.push(await (0, webview_1.setupWebviews)(languageServerManager, webviewMessageRegistry, aiContextManager));
    }
    else {
        const codeSuggestions = new code_suggestions_1.CodeSuggestions(context, dependencyContainer.gitLabPlatformManager);
        await codeSuggestions.init();
        context.subscriptions.push(codeSuggestions);
    }
    const glStateProvider = new gitlab_instance_version_provider_1.GitLabInstanceVersionProvider(dependencyContainer.gitLabPlatformManager);
    extensionStateService.addStateProvider(version_diagnostics_renderer_1.VersionDetailsStateKey, new version_state_provider_1.VersionStateProvider(context.extension.packageJSON.version, languageServerManager, glStateProvider));
    diagnosticsService.addRenderer(new version_diagnostics_renderer_1.VersionDiagnosticsRenderer());
    extensionStateService.addStateProvider(language_server_feature_state_provider_1.LanguageServerFeatureStateKey, languageServerFeatureStateProvider);
    diagnosticsService.addRenderer(new feature_state_diagnostics_renderer_1.FeatureStateDiagnosticsRenderer());
    extensionStateService.addStateProvider(settings_state_provider_1.SettingsDetailsStateKey, new settings_state_provider_1.SettingsStateProvider(extension_configuration_service_1.extensionConfigurationService));
    diagnosticsService.addRenderer(new settings_diagnostics_renderer_1.SettingsStateDiagnosticsRenderer());
    await (0, main_1.activateCommon)(context, dependencyContainer, outputChannel, aiContextManager, diagnosticsService, languageServerFeatureStateProvider);
};
exports.activate = activate;
//# sourceMappingURL=browser.js.map