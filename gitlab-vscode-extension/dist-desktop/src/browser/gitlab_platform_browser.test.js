"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const vscode = __importStar(require("vscode"));
const web_ide_1 = require("../common/platform/web_ide");
const entities_1 = require("../common/test_utils/entities");
const entities_2 = require("../desktop/test_utils/entities");
const action_cable_1 = require("../common/gitlab/api/action_cable");
const gitlab_platform_browser_1 = require("./gitlab_platform_browser");
jest.mock('../common/gitlab/api/action_cable');
const FAKE_MEDIATOR_TOKEN = 'fake-mediator-token';
const FAKE_WEB_IDE_EXTENSION_CONFIG = {
    isTelemetryEnabled() {
        return true;
    },
    projectPath: 'gitlab-org/gitlab',
    gitlabUrl: 'https://gitlab.com',
};
describe('createGitLabPlatformManagerBrowser', () => {
    const mockCommandsForInitialSetup = () => {
        jest.mocked(vscode.commands.executeCommand).mockImplementation(async (cmd, token, arg) => {
            // what: token arg isn't used in mock implementation, but we add assertion in the `it`
            if (cmd === web_ide_1.COMMAND_FETCH_FROM_API &&
                arg?.variables?.namespaceWithPath === 'gitlab-org/gitlab') {
                return { project: entities_1.gqlProject };
            }
            if (cmd === web_ide_1.COMMAND_FETCH_FROM_API && arg?.path === '/user') {
                return { user: entities_2.user };
            }
            if (cmd === web_ide_1.COMMAND_MEDIATOR_TOKEN) {
                return FAKE_MEDIATOR_TOKEN;
            }
            throw new Error(`Unexpected command ${cmd} with arg ${JSON.stringify(arg)}`);
        });
    };
    let manager;
    beforeEach(async () => {
        mockCommandsForInitialSetup();
        manager = await (0, gitlab_platform_browser_1.createGitLabPlatformManagerBrowser)(FAKE_WEB_IDE_EXTENSION_CONFIG);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('fetchFromApi', () => {
        describe('without GitLab hosted project', () => {
            let platform;
            beforeEach(async () => {
                platform = await manager.getForActiveAccount(false);
            });
            it('forwards all calls to fetchFromApi to the mediator command', async () => {
                expect(platform).toBeDefined();
                jest.resetAllMocks();
                const testRequest = { type: 'rest', method: 'GET', path: '/test' };
                const testResponse = { value: 'test' };
                jest.mocked(vscode.commands.executeCommand).mockResolvedValue(testResponse);
                const result = await platform?.fetchFromApi(testRequest);
                expect(result).toEqual(testResponse);
                expect(vscode.commands.executeCommand).toHaveBeenCalledWith(web_ide_1.COMMAND_FETCH_FROM_API, FAKE_MEDIATOR_TOKEN, testRequest);
            });
        });
        describe('with GitLab hosted project', () => {
            let platform;
            beforeEach(async () => {
                platform = await manager.getForActiveProject(false);
            });
            it('forwards all calls to fetchFromApi to the mediator command', async () => {
                expect(platform).toBeDefined();
                jest.resetAllMocks();
                const testRequest = { type: 'rest', method: 'GET', path: '/test' };
                const testResponse = { value: 'test' };
                jest.mocked(vscode.commands.executeCommand).mockResolvedValue(testResponse);
                const result = await platform?.fetchFromApi(testRequest);
                expect(result).toEqual(testResponse);
                expect(vscode.commands.executeCommand).toHaveBeenCalledWith(web_ide_1.COMMAND_FETCH_FROM_API, FAKE_MEDIATOR_TOKEN, testRequest);
            });
        });
    });
    describe('connectToCable', () => {
        it('delegates to api/action_cable', async () => {
            const platform = await manager.getForActiveAccount(false);
            expect(platform).toBeDefined();
            await platform?.connectToCable();
            expect(action_cable_1.connectToCable).toHaveBeenCalledWith('https://gitlab.com');
        });
    });
});
//# sourceMappingURL=gitlab_platform_browser.test.js.map