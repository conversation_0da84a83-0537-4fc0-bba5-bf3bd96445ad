"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createGitLabPlatformManagerBrowser = void 0;
const get_project_1 = require("../common/gitlab/api/get_project");
const get_current_user_1 = require("../common/gitlab/api/get_current_user");
const action_cable_1 = require("../common/gitlab/api/action_cable");
const auth_1 = require("./auth");
const api_1 = require("./api");
const createAccountPlatform = (user, gitlabUrl, authentication, apiClient) => {
    const token = authentication.getSession().accessToken;
    const account = {
        type: 'token',
        username: user.username,
        id: `${user.id}`,
        token,
        instanceUrl: gitlabUrl,
    };
    return {
        type: 'account',
        fetchFromApi: apiClient.fetchFromApi.bind(apiClient),
        connectToCable: async () => (0, action_cable_1.connectToCable)(account.instanceUrl),
        // browser won't let us change User-Agent header
        // so we don't have to construct it
        getUserAgentHeader: () => ({}),
        account,
        project: undefined,
    };
};
const createGitLabPlatformManagerBrowser = async ({ projectPath, gitlabUrl, }) => {
    const authentication = await (0, auth_1.resolveAuthentication)();
    const apiClient = (0, api_1.createApiClient)(gitlabUrl, authentication);
    const { project } = await apiClient.fetchFromApi((0, get_project_1.getProject)(projectPath));
    if (!project) {
        throw new Error(`GitLab API returned empty response when asked for ${projectPath} project.`);
    }
    const user = await apiClient.fetchFromApi(get_current_user_1.currentUserRequest);
    if (!user) {
        throw new Error(`GitLab API returned empty response when asked for the current user.`);
    }
    const gitLabProject = (0, get_project_1.**********************)(project);
    let accountPlatform = createAccountPlatform(user, gitlabUrl, authentication, apiClient);
    authentication.onChange(() => {
        accountPlatform = createAccountPlatform(user, gitlabUrl, authentication, apiClient);
    });
    return {
        getForActiveProject: () => Promise.resolve({
            ...accountPlatform,
            type: 'project',
            project: gitLabProject,
        }),
        getForActiveAccount: async () => accountPlatform,
        onAccountChange: authentication.onChange,
    };
};
exports.createGitLabPlatformManagerBrowser = createGitLabPlatformManagerBrowser;
//# sourceMappingURL=gitlab_platform_browser.js.map