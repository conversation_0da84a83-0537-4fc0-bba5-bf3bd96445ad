"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.resolveAuthentication = void 0;
const noop_authentication_1 = require("./noop_authentication");
const web_ide_authentication_1 = require("./web_ide_authentication");
const resolveAuthentication = async () => {
    const authSession = await (0, web_ide_authentication_1.getWebIdeAuthSession)();
    if (!authSession) {
        return new noop_authentication_1.NoopAuthentication();
    }
    return new web_ide_authentication_1.WebIdeAuthentication(authSession);
};
exports.resolveAuthentication = resolveAuthentication;
//# sourceMappingURL=factory.js.map