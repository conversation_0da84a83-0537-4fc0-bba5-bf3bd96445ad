"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const create_fake_partial_1 = require("../../common/test_utils/create_fake_partial");
const factory_1 = require("./factory");
const noop_authentication_1 = require("./noop_authentication");
const web_ide_authentication_1 = require("./web_ide_authentication");
jest.mock('./web_ide_authentication');
const TEST_AUTH_SESSION = (0, create_fake_partial_1.createFakePartial)({
    accessToken: 'test-access-token',
    id: 'test-id',
    scopes: ['test-scope'],
});
describe('browser/auth/factory', () => {
    describe('resolveAuthentication', () => {
        it('with no auth session, returns noop authentication', async () => {
            const actual = await (0, factory_1.resolveAuthentication)();
            expect(actual).toBeInstanceOf(noop_authentication_1.NoopAuthentication);
        });
        it('with web ide auth session, returns web ide authentication', async () => {
            jest.mocked(web_ide_authentication_1.getWebIdeAuthSession).mockResolvedValue(TEST_AUTH_SESSION);
            const actual = await (0, factory_1.resolveAuthentication)();
            expect(actual).toBeInstanceOf(web_ide_authentication_1.WebIdeAuthentication);
            expect(web_ide_authentication_1.WebIdeAuthentication).toHaveBeenCalledWith(TEST_AUTH_SESSION);
        });
    });
});
//# sourceMappingURL=factory.test.js.map