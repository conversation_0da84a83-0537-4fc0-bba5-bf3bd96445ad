{"version": 3, "file": "gitlab_platform_browser.js", "sourceRoot": "", "sources": ["../../../src/browser/gitlab_platform_browser.ts"], "names": [], "mappings": ";;;AAKA,kEAAsF;AACtF,4EAA2E;AAE3E,oEAAmE;AAGnE,iCAA+C;AAC/C,+BAAwC;AAExC,MAAM,qBAAqB,GAAG,CAC5B,IAAc,EACd,SAAiB,EACjB,cAA8B,EAC9B,SAAoB,EACM,EAAE;IAC5B,MAAM,KAAK,GAAG,cAAc,CAAC,UAAU,EAAE,CAAC,WAAW,CAAC;IAEtD,MAAM,OAAO,GAAiB;QAC5B,IAAI,EAAE,OAAO;QACb,QAAQ,EAAE,IAAI,CAAC,QAAQ;QACvB,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE;QAChB,KAAK;QACL,WAAW,EAAE,SAAS;KACvB,CAAC;IAEF,OAAO;QACL,IAAI,EAAE,SAAS;QACf,YAAY,EAAE,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;QACpD,cAAc,EAAE,KAAK,IAAI,EAAE,CAAC,IAAA,6BAAc,EAAC,OAAO,CAAC,WAAW,CAAC;QAC/D,gDAAgD;QAChD,mCAAmC;QACnC,kBAAkB,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC;QAC9B,OAAO;QACP,OAAO,EAAE,SAAS;KACnB,CAAC;AACJ,CAAC,CAAC;AAEK,MAAM,kCAAkC,GAAG,KAAK,EAAE,EACvD,WAAW,EACX,SAAS,GACO,EAAkC,EAAE;IACpD,MAAM,cAAc,GAAG,MAAM,IAAA,4BAAqB,GAAE,CAAC;IAErD,MAAM,SAAS,GAAG,IAAA,qBAAe,EAAC,SAAS,EAAE,cAAc,CAAC,CAAC;IAE7D,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,IAAA,wBAAU,EAAC,WAAW,CAAC,CAAC,CAAC;IAC1E,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,MAAM,IAAI,KAAK,CAAC,qDAAqD,WAAW,WAAW,CAAC,CAAC;IAC/F,CAAC;IAED,MAAM,IAAI,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,qCAAkB,CAAC,CAAC;IAC9D,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;IACzF,CAAC;IAED,MAAM,aAAa,GAAG,IAAA,oCAAsB,EAAC,OAAO,CAAC,CAAC;IAEtD,IAAI,eAAe,GAAG,qBAAqB,CAAC,IAAI,EAAE,SAAS,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC;IACxF,cAAc,CAAC,QAAQ,CAAC,GAAG,EAAE;QAC3B,eAAe,GAAG,qBAAqB,CAAC,IAAI,EAAE,SAAS,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC;IACtF,CAAC,CAAC,CAAC;IAEH,OAAO;QACL,mBAAmB,EAAE,GAAG,EAAE,CACxB,OAAO,CAAC,OAAO,CAAC;YACd,GAAG,eAAe;YAClB,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,aAAa;SACvB,CAAC;QACJ,mBAAmB,EAAE,KAAK,IAAI,EAAE,CAAC,eAAe;QAChD,eAAe,EAAE,cAAc,CAAC,QAAQ;KACzC,CAAC;AACJ,CAAC,CAAC;AAnCW,QAAA,kCAAkC,sCAmC7C"}