{"version": 3, "file": "gitlab_telemetry_environment_browser.test.js", "sourceRoot": "", "sources": ["../../../src/browser/gitlab_telemetry_environment_browser.test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC,kFAA6E;AAC7E,0EAAuF;AACvF,iGAA2F;AAE3F,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;IACjD,IAAI,OAA0C,CAAC;IAC/C,IAAI,mBAAoC,CAAC;IAEzC,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,mBAAmB,GAAG,IAAA,uCAAiB,EAAkB;YACvD,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;SAC9B,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,mBAAmB,CAC7D,IAAA,uCAAiB,EAAoC;YACnD,OAAO,EAAE,mBAAmB;SAC7B,CAAC,CACH,CAAC;QACF,OAAO,GAAG,IAAI,wEAAiC,CAAC,mBAAmB,CAAC,CAAC;IACvE,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,IAAI,CAAA;;QAEH,IAAI;QACJ,KAAK;KACR,CACC,2GAA2G,EAC3G,KAAK,EAAE,EAAE,eAAe,EAAE,EAAE,EAAE;YAC5B,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;YAEzF,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC7D,CAAC,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;QAChE,MAAM,CAAC,OAAO,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC;YAC9D,MAAM,EAAE,mDAAgC;YACxC,IAAI,EAAE;gBACJ,QAAQ,EAAE,gBAAgB;gBAC1B,UAAU,EAAE,aAAa;gBACzB,WAAW,EAAE,yBAAyB;gBACtC,cAAc,EAAE,iBAAiB;gBACjC,iBAAiB,EAAE,OAAO;aAC3B;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}