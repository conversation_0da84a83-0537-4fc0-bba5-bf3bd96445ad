"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const noop_authentication_1 = require("../auth/noop_authentication");
const api_client_1 = require("../../common/gitlab/api/api_client");
const create_fake_partial_1 = require("../../common/test_utils/create_fake_partial");
const factory_1 = require("./factory");
const mediator_commands_api_client_1 = require("./mediator_commands_api_client");
jest.mock('../../common/gitlab/api/api_client');
const TEST_INSTANCE_URL = 'http://localhost:3000';
const TEST_AUTHENTICATION = (0, create_fake_partial_1.createFakePartial)({
    getSession: () => ({
        accessToken: 'test-token',
        account: {
            id: 'test-id',
            label: 'test-label',
        },
        id: 'test-id',
        scopes: ['api'],
    }),
});
describe('browser/api/factory', () => {
    describe('createApiClient', () => {
        it('when auth does not have accessToken, creates MediatorCommandsApiClient', () => {
            const client = (0, factory_1.createApiClient)(TEST_INSTANCE_URL, new noop_authentication_1.NoopAuthentication());
            expect(client).toBeInstanceOf(mediator_commands_api_client_1.MediatorCommandsApiClient);
        });
        it('when auth has accessToken, creates DefaultApiClient', () => {
            const client = (0, factory_1.createApiClient)(TEST_INSTANCE_URL, TEST_AUTHENTICATION);
            expect(client).toBeInstanceOf(api_client_1.DefaultApiClient);
            expect(api_client_1.DefaultApiClient).toHaveBeenCalledWith({
                instanceUrl: TEST_INSTANCE_URL,
                authProvider: {
                    getAuthHeaders: expect.any(Function),
                },
            });
        });
        it('when auth has accessToken, authProvider.getAuthHeaders returns correct headers', async () => {
            (0, factory_1.createApiClient)(TEST_INSTANCE_URL, TEST_AUTHENTICATION);
            const headers = await jest
                .mocked(api_client_1.DefaultApiClient)
                .mock.calls[0]?.[0]?.authProvider?.getAuthHeaders();
            expect(headers).toEqual({
                Authorization: 'Bearer test-token',
            });
        });
    });
});
//# sourceMappingURL=factory.test.js.map