"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createApiClient = void 0;
const api_client_1 = require("../../common/gitlab/api/api_client");
const mediator_commands_api_client_1 = require("./mediator_commands_api_client");
const createApiClient = (instanceUrl, authentication) => {
    // note: We only need to check this once here to know if we support auth tokens or not
    const hasAuthToken = Boolean(authentication.getSession().accessToken);
    if (hasAuthToken) {
        return new api_client_1.DefaultApiClient({
            instanceUrl,
            authProvider: {
                async getAuthHeaders() {
                    // note: It's important that we *refetch* `getSession` here, to make sure we have the latest.
                    return {
                        Authorization: `Bearer ${authentication.getSession().accessToken}`,
                    };
                },
            },
        });
    }
    return new mediator_commands_api_client_1.MediatorCommandsApiClient();
};
exports.createApiClient = createApiClient;
//# sourceMappingURL=factory.js.map