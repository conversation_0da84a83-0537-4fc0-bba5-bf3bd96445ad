"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createDependencyContainer = void 0;
const ls_git_provider_1 = require("../common/git/ls_git_provider");
const gitlab_platform_browser_1 = require("./gitlab_platform_browser");
const gitlab_telemetry_environment_browser_1 = require("./gitlab_telemetry_environment_browser");
const createDependencyContainer = async (webIdeExtension) => ({
    gitLabPlatformManager: await (0, gitlab_platform_browser_1.createGitLabPlatformManagerBrowser)(webIdeExtension),
    gitLabTelemetryEnvironment: new gitlab_telemetry_environment_browser_1.GitLabTelemetryEnvironmentBrowser(webIdeExtension),
    lsGitProvider: new ls_git_provider_1.DefaultLSGitProvider(),
});
exports.createDependencyContainer = createDependencyContainer;
//# sourceMappingURL=dependency_container_browser.js.map