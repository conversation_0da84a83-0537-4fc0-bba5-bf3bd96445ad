"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GitLabTelemetryEnvironmentBrowser = void 0;
const vscode_1 = __importDefault(require("vscode"));
const snowplow_options_1 = require("../common/snowplow/snowplow_options");
class GitLabTelemetryEnvironmentBrowser {
    #webIdeExtension;
    constructor(webIdeExtension) {
        this.#webIdeExtension = webIdeExtension;
    }
    isTelemetryEnabled() {
        return this.#webIdeExtension?.isTelemetryEnabled() || false;
    }
    buildIdeExtensionContext(extVersion) {
        return {
            schema: snowplow_options_1.IDE_EXTENSION_VERSION_SCHEMA_URL,
            data: {
                ide_name: 'GitLab Web IDE',
                ide_vendor: 'GitLab Inc.',
                ide_version: vscode_1.default.version,
                extension_name: 'GitLab Workflow',
                extension_version: extVersion,
            },
        };
    }
    get onDidChangeTelemetryEnabled() {
        return this.#onDidChangeTelemetryEnabled.event;
    }
    // FIXME: implement emitting event when telemetry enabled state changes
    // sync of the extension config with the LS happens on this event
    // https://gitlab.com/gitlab-org/gitlab-web-ide/-/issues/352
    #onDidChangeTelemetryEnabled = new vscode_1.default.EventEmitter();
}
exports.GitLabTelemetryEnvironmentBrowser = GitLabTelemetryEnvironmentBrowser;
//# sourceMappingURL=gitlab_telemetry_environment_browser.js.map