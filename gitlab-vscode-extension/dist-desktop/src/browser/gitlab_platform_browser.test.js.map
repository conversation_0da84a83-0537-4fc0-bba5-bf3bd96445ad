{"version": 3, "file": "gitlab_platform_browser.test.js", "sourceRoot": "", "sources": ["../../../src/browser/gitlab_platform_browser.test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,wDAIoC;AACpC,4DAA2D;AAM3D,6DAAsD;AACtD,oEAAmE;AACnE,uEAA+E;AAE/E,IAAI,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;AAE/C,MAAM,mBAAmB,GAAG,qBAAqB,CAAC;AAClD,MAAM,6BAA6B,GAAoB;IACrD,kBAAkB;QAChB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,WAAW,EAAE,mBAAmB;IAChC,SAAS,EAAE,oBAAoB;CAChC,CAAC;AAEF,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;IAClD,MAAM,2BAA2B,GAAG,GAAG,EAAE;QACvC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,kBAAkB,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE;YACvF,sFAAsF;YACtF,IACE,GAAG,KAAK,gCAAsB;gBAC9B,GAAG,EAAE,SAAS,EAAE,iBAAiB,KAAK,mBAAmB,EACzD,CAAC;gBACD,OAAO,EAAE,OAAO,EAAE,qBAAU,EAAE,CAAC;YACjC,CAAC;YACD,IAAI,GAAG,KAAK,gCAAsB,IAAI,GAAG,EAAE,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC5D,OAAO,EAAE,IAAI,EAAJ,eAAI,EAAE,CAAC;YAClB,CAAC;YACD,IAAI,GAAG,KAAK,gCAAsB,EAAE,CAAC;gBACnC,OAAO,mBAAmB,CAAC;YAC7B,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,sBAAsB,GAAG,aAAa,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,IAAI,OAA8B,CAAC;IAEnC,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,2BAA2B,EAAE,CAAC;QAE9B,OAAO,GAAG,MAAM,IAAA,4DAAkC,EAAC,6BAA6B,CAAC,CAAC;IACpF,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;YAC7C,IAAI,QAA8C,CAAC;YAEnD,UAAU,CAAC,KAAK,IAAI,EAAE;gBACpB,QAAQ,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,4DAA4D,EAAE,KAAK,IAAI,EAAE;gBAC1E,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;gBAE/B,IAAI,CAAC,aAAa,EAAE,CAAC;gBAErB,MAAM,WAAW,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAW,CAAC;gBAC5E,MAAM,YAAY,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;gBAEvC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;gBAE5E,MAAM,MAAM,GAAG,MAAM,QAAQ,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC;gBAEzD,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;gBACrC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,oBAAoB,CACzD,gCAAsB,EACtB,mBAAmB,EACnB,WAAW,CACZ,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;YAC1C,IAAI,QAA8C,CAAC;YAEnD,UAAU,CAAC,KAAK,IAAI,EAAE;gBACpB,QAAQ,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,4DAA4D,EAAE,KAAK,IAAI,EAAE;gBAC1E,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;gBAE/B,IAAI,CAAC,aAAa,EAAE,CAAC;gBAErB,MAAM,WAAW,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAW,CAAC;gBAC5E,MAAM,YAAY,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;gBAEvC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;gBAE5E,MAAM,MAAM,GAAG,MAAM,QAAQ,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC;gBAEzD,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;gBACrC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,oBAAoB,CACzD,gCAAsB,EACtB,mBAAmB,EACnB,WAAW,CACZ,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAE1D,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAE/B,MAAM,QAAQ,EAAE,cAAc,EAAE,CAAC;YAEjC,MAAM,CAAC,6BAAc,CAAC,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}