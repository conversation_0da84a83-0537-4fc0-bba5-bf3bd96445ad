"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupYamlSupport = void 0;
const vscode = __importStar(require("vscode"));
const user_message_1 = require("../common/user_message");
const constants_1 = require("./constants");
const setupYamlSupport = (context) => {
    if (vscode.extensions.getExtension('redhat.vscode-yaml'))
        return;
    const yamlMessage = new user_message_1.UserMessage(context.globalState, constants_1.DO_NOT_SHOW_YAML_SUGGESTION, "Would you like to install Red Hat's YAML extension to get real-time linting on the .gitlab-ci.yml file?", [
        {
            title: 'Yes',
            callback: async () => {
                await vscode.commands.executeCommand('workbench.extensions.installExtension', 'redhat.vscode-yaml');
            },
        },
        {
            title: 'Not now',
            callback: () => { }, // No-op
        },
    ]);
    vscode.workspace.onDidOpenTextDocument(async (document) => {
        if (document.fileName.endsWith('.gitlab-ci.yml')) {
            await yamlMessage.trigger();
        }
    });
};
exports.setupYamlSupport = setupYamlSupport;
//# sourceMappingURL=yaml_support.js.map