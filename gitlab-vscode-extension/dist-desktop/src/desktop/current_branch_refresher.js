"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.currentBranchRefresher = exports.CurrentBranchRefresher = void 0;
const vscode = __importStar(require("vscode"));
const dayjs_1 = __importDefault(require("dayjs"));
const log_1 = require("../common/log");
const not_null_or_undefined_1 = require("../common/utils/not_null_or_undefined");
const local_feature_flag_service_1 = require("../common/feature_flags/local_feature_flag_service");
const user_friendly_error_1 = require("../common/errors/user_friendly_error");
const extension_state_1 = require("./extension_state");
const run_with_valid_project_1 = require("./commands/run_with_valid_project");
const get_gitlab_service_1 = require("./gitlab/get_gitlab_service");
const get_tracking_branch_name_1 = require("./git/get_tracking_branch_name");
const get_current_branch_1 = require("./git/get_current_branch");
const get_tags_for_head_1 = require("./git/get_tags_for_head");
const detached_head_error_1 = require("./errors/detached_head_error");
const get_pipelines_for_ref_1 = require("./gitlab/api/get_pipelines_for_ref");
const get_pipeline_and_mr_for_branch_1 = require("./gitlab/get_pipeline_and_mr_for_branch");
const get_all_security_reports_1 = require("./gitlab/security_findings/get_all_security_reports");
const INVALID_STATE = { type: 'invalid' };
const getJobs = async (projectInRepository, pipeline) => {
    if (!pipeline)
        return [];
    try {
        const projectId = projectInRepository.project.restId;
        const service = (0, get_gitlab_service_1.getGitLabService)(projectInRepository);
        const pipelinePromise = service.getJobsForPipeline(pipeline.id, projectId);
        const bridgesPromise = service.getTriggerJobsForPipeline(pipeline.id, projectId);
        const statusPromise = service.getExternalStatusForCommit(pipeline.sha, pipeline.ref, projectId);
        return [...(await pipelinePromise), ...(await bridgesPromise), ...(await statusPromise)];
    }
    catch (e) {
        log_1.log.error(new user_friendly_error_1.UserFriendlyError('Failed to fetch jobs for pipeline.', e));
        return [];
    }
};
class CurrentBranchRefresher {
    #refreshTimer;
    #branchTrackingTimer;
    #stateChangedEmitter = new vscode.EventEmitter();
    onStateChanged = this.#stateChangedEmitter.event;
    #lastRefresh = (0, dayjs_1.default)().subtract(1, 'minute');
    #previousBranchName = '';
    #latestState = INVALID_STATE;
    init() {
        this.clearAndSetInterval();
        // FIXME: the extension state should be passed in a constructor, not used as a singleton
        (0, extension_state_1.getExtensionStateSingleton)().onDidChangeValid(() => this.clearAndSetIntervalAndRefresh());
        vscode.window.onDidChangeWindowState(async (state) => {
            if (!state.focused) {
                return;
            }
            if ((0, dayjs_1.default)().diff(this.#lastRefresh, 'second') > 30) {
                await this.clearAndSetIntervalAndRefresh();
            }
        });
        // This polling is not ideal. The alternative is to listen on repository state
        // changes. The logic becomes much more complex and the state changes
        // (Repository.state.onDidChange()) are triggered many times per second.
        // We wouldn't save any CPU cycles, just increased the complexity of this extension.
        this.#branchTrackingTimer = setInterval(async () => {
            const projectInRepository = (0, run_with_valid_project_1.getActiveProject)();
            const currentBranch = projectInRepository &&
                (0, get_current_branch_1.getCurrentBranchName)(projectInRepository.pointer.repository.rawRepository);
            if (currentBranch && currentBranch !== this.#previousBranchName) {
                this.#previousBranchName = currentBranch;
                await this.clearAndSetIntervalAndRefresh();
            }
        }, 1000);
    }
    async clearAndSetIntervalAndRefresh() {
        await this.clearAndSetInterval();
        await this.refresh();
    }
    clearAndSetInterval() {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        global.clearInterval(this.#refreshTimer);
        this.#refreshTimer = setInterval(async () => {
            if (!vscode.window.state.focused)
                return;
            await this.refresh();
        }, 30000);
    }
    async refresh(userInitiated = false) {
        const projectInRepository = (0, run_with_valid_project_1.getActiveProject)();
        this.#latestState = await CurrentBranchRefresher.getState(projectInRepository, userInitiated);
        this.#stateChangedEmitter.fire(this.#latestState);
        this.#lastRefresh = (0, dayjs_1.default)();
    }
    async getOrRetrieveState() {
        if (this.#latestState.type === 'invalid') {
            await this.refresh(false);
        }
        return this.#latestState;
    }
    static async getPipelineAndMrForHead(gitLabService, projectInRepository) {
        const { rawRepository } = projectInRepository.pointer.repository;
        const branchName = await (0, get_tracking_branch_name_1.getTrackingBranchName)(rawRepository);
        if (branchName) {
            const { pipeline, mr } = await (0, get_pipeline_and_mr_for_branch_1.getPipelineAndMrForBranch)(gitLabService, projectInRepository.project, branchName);
            return { type: 'branch', pipeline, mr };
        }
        const tags = await (0, get_tags_for_head_1.getTagsForHead)(rawRepository);
        if (tags.length === 1) {
            const pipelines = await gitLabService.fetchFromApi((0, get_pipelines_for_ref_1.getPipelinesForRef)(projectInRepository.project, tags[0]));
            return {
                type: 'tag',
                pipeline: pipelines[0],
            };
        }
        throw new detached_head_error_1.DetachedHeadError(tags);
    }
    static async getState(projectInRepository, userInitiated) {
        if (!projectInRepository)
            return INVALID_STATE;
        const { project } = projectInRepository;
        const gitLabService = (0, get_gitlab_service_1.getGitLabService)(projectInRepository);
        let securityFindings;
        try {
            const { type, pipeline, mr } = await CurrentBranchRefresher.getPipelineAndMrForHead(gitLabService, projectInRepository);
            const jobs = await getJobs(projectInRepository, pipeline);
            const minimalIssues = mr ? await gitLabService.getMrClosingIssues(project, mr.iid) : [];
            if (mr && (0, local_feature_flag_service_1.getLocalFeatureFlagService)().isEnabled(local_feature_flag_service_1.FeatureFlag.SecurityScans)) {
                securityFindings = await (0, get_all_security_reports_1.getAllSecurityReports)(gitLabService, projectInRepository.project, mr);
            }
            const issues = (await Promise.all(minimalIssues
                .map(mi => mi.iid)
                .filter(not_null_or_undefined_1.notNullOrUndefined)
                .map(iid => gitLabService.getSingleProjectIssue(project, iid)))).filter(not_null_or_undefined_1.notNullOrUndefined);
            return {
                type,
                projectInRepository,
                pipeline,
                mr,
                jobs,
                issues,
                userInitiated,
                securityFindings,
            };
        }
        catch (e) {
            log_1.log.error(e);
            return { type: 'invalid', error: e };
        }
    }
    stopTimers() {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        global.clearInterval(this.#refreshTimer);
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        global.clearInterval(this.#branchTrackingTimer);
    }
    dispose() {
        this.stopTimers();
        this.#stateChangedEmitter.dispose();
    }
}
exports.CurrentBranchRefresher = CurrentBranchRefresher;
exports.currentBranchRefresher = new CurrentBranchRefresher();
//# sourceMappingURL=current_branch_refresher.js.map