{"version": 3, "file": "status_bar.test.js", "sourceRoot": "", "sources": ["../../../src/desktop/status_bar.test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,4DAAwD;AACxD,kFAA6E;AAC7E,oDAAiE;AACjE,mDAAgD;AAIhD,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;AACzC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAE/B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAC5D,IAAA,uCAAiB,EAAgC;IAC/C,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;IACd,kBAAkB,EAAE,IAAI;IACxB,wBAAwB,EAAE,IAAI;IAC9B,uBAAuB,EAAE,IAAI;CAC9B,CAAC,CACH,CAAC;AAEF,mGAAmG;AACnG,+BAA+B;AAC/B,wCAAwC;AACxC,6CAAyC;AAEzC,MAAM,cAAc,GAAG,GAAyB,EAAE,CAChD,CAAC;IACC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;IACf,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;IACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;CACnB,CAAoC,CAAC;AAExC,MAAM,gBAAgB,GAAG,CAAC,cAAoC,EAAE,EAAe,EAAE,CAAC,CAAC;IACjF,IAAI,EAAE,QAAQ;IACd,mBAAmB,EAAE,EAAE,OAAO,EAAP,kBAAO,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,EAAoC;IAC/F,MAAM,EAAE,EAAE;IACV,IAAI,EAAE,EAAE;IACR,aAAa,EAAE,IAAI;IACnB,GAAG,WAAW;CACf,CAAC,CAAC;AAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;IAC1B,IAAI,SAAiC,CAAC;IACtC,IAAI,SAAoB,CAAC;IACzB,MAAM,eAAe,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC3C,MAAM,SAAS,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IACrC,MAAM,mBAAmB,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAE/C,UAAU,CAAC,GAAG,EAAE;QACd,SAAS,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,kBAAkB,CAAC,GAAG,EAAE;YACrE,MAAM,QAAQ,GAAG,cAAc,EAAE,CAAC;YAClC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACzB,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC,CAAC;QACH,SAAS,GAAG,IAAI,sBAAS,EAAE,CAAC;QAC5B,SAAS,CAAC,IAAI,EAAE,CAAC;IACnB,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,SAAS,CAAC,OAAO,EAAE,CAAC;IACtB,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;QAC3D,MAAM,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;QAC7C,MAAM,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAClD,MAAM,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAC5C,MAAM,CAAC,mBAAmB,EAAE,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,CAAC;IACxD,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,SAAS,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,QAAQ,EAAR,mBAAQ,EAAE,CAAC,CAAC,CAAC;YACxD,MAAM,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAClD,MAAM,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;YACtD,MAAM,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,IAAI,GAAG;gBACX;oBACE,GAAG,cAAG;oBACN,MAAM,EAAE,SAAS;oBACjB,IAAI,EAAE,YAAY;iBACnB;gBACD;oBACE,GAAG,cAAG;oBACN,MAAM,EAAE,SAAS;oBACjB,IAAI,EAAE,mBAAmB;iBAC1B;gBACD;oBACE,GAAG,cAAG;oBACN,MAAM,EAAE,SAAS;oBACjB,IAAI,EAAE,MAAM;iBACb;aACW,CAAC;YACf,MAAM,SAAS,CAAC,OAAO,CACrB,gBAAgB,CAAC,EAAE,QAAQ,EAAE,EAAE,GAAG,mBAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,CAAC,CACzE,CAAC;YACF,MAAM,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sFAAsF,EAAE,KAAK,IAAI,EAAE;YACpG,MAAM,IAAI,GAAG;gBACX;oBACE,GAAG,cAAG;oBACN,MAAM,EAAE,SAAS;oBACjB,IAAI,EAAE,mBAAmB;oBACzB,UAAU,EAAE,0BAA0B;iBACvC;gBACD;oBACE,GAAG,cAAG;oBACN,MAAM,EAAE,SAAS;oBACjB,IAAI,EAAE,YAAY;oBAClB,UAAU,EAAE,0BAA0B;iBACvC;gBACD;oBACE,GAAG,cAAG;oBACN,MAAM,EAAE,SAAS;oBACjB,IAAI,EAAE,YAAY;oBAClB,UAAU,EAAE,0BAA0B;iBACvC;aACW,CAAC;YACf,MAAM,SAAS,CAAC,OAAO,CACrB,gBAAgB,CAAC,EAAE,QAAQ,EAAE,EAAE,GAAG,mBAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,CAAC,CACzE,CAAC;YACF,MAAM,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,SAAS,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC;YAC5C,MAAM,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,IAAI,CAAA;;QAEH,SAAS,OAAO,2BAA2B;QAC3C,SAAS,OAAO,0BAA0B;QAC1C,SAAS,OAAO,2BAA2B;QAC3C,QAAQ,QAAQ,sBAAsB;QACtC,UAAU,MAAM,mCAAmC;QACnD,SAAS,OAAO,kCAAkC;KACrD,CAAC,kDAAkD,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE;YACnF,MAAM,SAAS,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,QAAQ,EAAE,EAAE,GAAG,mBAAQ,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC;YACjF,MAAM,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;QACvB,EAAE,CAAC,eAAe,EAAE,KAAK,IAAI,EAAE;YAC7B,MAAM,SAAS,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,EAAE,EAAF,aAAE,EAAE,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAC5C,MAAM,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;YAChD,MAAM,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAC3D,MAAM,OAAO,GAAG,SAAS,EAAE,CAAC,OAAyB,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAE,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,SAAS,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC;YAC5C,MAAM,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAC/D,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,6BAAa,CAAC,kBAAkB,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,SAAS,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,EAAE,EAAF,aAAE,EAAE,MAAM,EAAE,CAAC,gBAAK,CAAC,EAAE,CAAC,CAAC,CAAC;YACnE,MAAM,CAAC,mBAAmB,EAAE,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACtD,MAAM,CAAC,mBAAmB,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;YAC1D,MAAM,CAAC,mBAAmB,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACzD,MAAM,OAAO,GAAG,mBAAmB,EAAE,CAAC,OAAyB,CAAC;YAChE,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,gBAAK,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,SAAS,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,EAAE,EAAF,aAAE,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;YAC9D,MAAM,CAAC,mBAAmB,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC5D,MAAM,CAAC,mBAAmB,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,SAAS,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC;YAC5C,MAAM,CAAC,mBAAmB,EAAE,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}