{"version": 3, "file": "fetch.js", "sourceRoot": "", "sources": ["../../../src/desktop/fetch.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,gDAEC;AAED,wDAwDC;AA9ED,kDAA0B;AAC1B,gDAAwB;AACxB,+CAAiC;AAEjC,uCAAoC;AACpC,iFAA2E;AAE3E,MAAM,UAAU,GAAG,IAAI,eAAK,CAAC,KAAK,CAAC,IAAA,4CAAmB,GAAE,CAAC,CAAC;AAC1D,MAAM,SAAS,GAAG,IAAI,cAAI,CAAC,KAAK,CAAC,IAAA,4CAAmB,GAAE,CAAC,CAAC;AAExD,kEAAkE;AAClE,mEAAmE;AACnE,MAAM,WAAW,GAAG,sBAAsB,CAAC;AAC3C,MAAM,cAAc,GAAG,+BAA+B,CAAC;AACvD,MAAM,UAAU,GAAG,eAAe,CAAC;AACtB,QAAA,eAAe,GAAG,YAAY,CAAC;AAC5C,IAAI,YAAY,GAAG,EAAE,CAAC;AAEtB,SAAgB,kBAAkB;IAChC,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;AAC9E,CAAC;AAED,SAAgB,sBAAsB;IACpC,MAAM,WAAW,GAAG,CAAC,EAAE,QAAQ,EAAyB,EAAc,EAAE;QACtE,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;YACzB,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,OAAO,UAAU,CAAC;IACpB,CAAC,CAAC;IACF,kBAAkB,EAAE,CAAC;IACrB;;;;;;;;;;;;OAYG;IACH,IAAI,CAAC;QACH,MAAM,aAAa;QACjB,8DAA8D;QAC7D,UAAkB,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,SAAS,CAAC;QAClF,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,eAAe,GAAG,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC;YACxD,2CAA2C;YAC3C,iHAAiH;YACjH,aAAa,CAAC,SAAS,CAAC,OAAO,GAAG,UAChC,GAAuB,EACvB,IAA0B;gBAG1B,IAAI,CAAC;oBACH,MAAM,gBAAgB,GAAG,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;oBACrD,MAAM,4BAA4B,GAChC,gBAAgB,KAAK,uBAAe;wBACpC,CAAC,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,gBAAgB,CAAC,QAAQ,CAAC,uBAAe,CAAC,CAAC,CAAC;oBAElF,IAAI,4BAA4B,IAAI,YAAY,KAAK,EAAE,EAAE,CAAC;wBACxD,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;wBAC5C,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;oBAC/C,CAAC;oBACD,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;gBAC/C,CAAC;gBAAC,MAAM,CAAC;oBACP,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;gBAC/C,CAAC;YACH,CAAC,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,SAAG,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,uCAAuC;QACvC,SAAG,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;IACjD,CAAC;AACH,CAAC"}