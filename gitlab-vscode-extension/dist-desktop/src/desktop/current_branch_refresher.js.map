{"version": 3, "file": "current_branch_refresher.js", "sourceRoot": "", "sources": ["../../../src/desktop/current_branch_refresher.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,kDAA0B;AAC1B,uCAAoC;AACpC,iFAA2E;AAC3E,mGAG4D;AAC5D,8EAAyE;AACzE,uDAA+D;AAC/D,8EAAqE;AAErE,oEAA+D;AAC/D,6EAAuE;AACvE,iEAAgE;AAEhE,+DAAyD;AACzD,sEAAiE;AAEjE,8EAAwE;AACxE,4FAAoF;AACpF,kGAA4F;AA4B5F,MAAM,aAAa,GAAiB,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;AAExD,MAAM,OAAO,GAAG,KAAK,EACnB,mBAAwC,EACxC,QAAuB,EACH,EAAE;IACtB,IAAI,CAAC,QAAQ;QAAE,OAAO,EAAE,CAAC;IACzB,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,mBAAmB,CAAC,OAAO,CAAC,MAAM,CAAC;QACrD,MAAM,OAAO,GAAG,IAAA,qCAAgB,EAAC,mBAAmB,CAAC,CAAC;QAEtD,MAAM,eAAe,GAAG,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QAC3E,MAAM,cAAc,GAAG,OAAO,CAAC,yBAAyB,CAAC,QAAQ,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QACjF,MAAM,aAAa,GAAG,OAAO,CAAC,0BAA0B,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QAChG,OAAO,CAAC,GAAG,CAAC,MAAM,eAAe,CAAC,EAAE,GAAG,CAAC,MAAM,cAAc,CAAC,EAAE,GAAG,CAAC,MAAM,aAAa,CAAC,CAAC,CAAC;IAC3F,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,SAAG,CAAC,KAAK,CAAC,IAAI,uCAAiB,CAAC,oCAAoC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1E,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC,CAAC;AAEF,MAAa,sBAAsB;IACjC,aAAa,CAAkB;IAE/B,oBAAoB,CAAkB;IAEtC,oBAAoB,GAAG,IAAI,MAAM,CAAC,YAAY,EAAa,CAAC;IAE5D,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;IAEjD,YAAY,GAAG,IAAA,eAAK,GAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;IAE7C,mBAAmB,GAAG,EAAE,CAAC;IAEzB,YAAY,GAAc,aAAa,CAAC;IAExC,IAAI;QACF,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,wFAAwF;QACxF,IAAA,4CAA0B,GAAE,CAAC,gBAAgB,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,6BAA6B,EAAE,CAAC,CAAC;QAC1F,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,KAAK,EAAC,KAAK,EAAC,EAAE;YACjD,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBACnB,OAAO;YACT,CAAC;YACD,IAAI,IAAA,eAAK,GAAE,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC;gBACnD,MAAM,IAAI,CAAC,6BAA6B,EAAE,CAAC;YAC7C,CAAC;QACH,CAAC,CAAC,CAAC;QACH,8EAA8E;QAC9E,qEAAqE;QACrE,wEAAwE;QACxE,oFAAoF;QACpF,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YACjD,MAAM,mBAAmB,GAAG,IAAA,yCAAgB,GAAE,CAAC;YAC/C,MAAM,aAAa,GACjB,mBAAmB;gBACnB,IAAA,yCAAoB,EAAC,mBAAmB,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAC7E,IAAI,aAAa,IAAI,aAAa,KAAK,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAChE,IAAI,CAAC,mBAAmB,GAAG,aAAa,CAAC;gBACzC,MAAM,IAAI,CAAC,6BAA6B,EAAE,CAAC;YAC7C,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,CAAC;IACX,CAAC;IAED,KAAK,CAAC,6BAA6B;QACjC,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACjC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;IACvB,CAAC;IAED,mBAAmB;QACjB,oEAAoE;QACpE,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,aAAc,CAAC,CAAC;QAC1C,IAAI,CAAC,aAAa,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAC1C,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO;gBAAE,OAAO;YACzC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC,EAAE,KAAK,CAAC,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,aAAa,GAAG,KAAK;QACjC,MAAM,mBAAmB,GAAG,IAAA,yCAAgB,GAAE,CAAC;QAC/C,IAAI,CAAC,YAAY,GAAG,MAAM,sBAAsB,CAAC,QAAQ,CAAC,mBAAmB,EAAE,aAAa,CAAC,CAAC;QAC9F,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAClD,IAAI,CAAC,YAAY,GAAG,IAAA,eAAK,GAAE,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACzC,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC;QACD,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAClC,aAA4B,EAC5B,mBAAwC;QAExC,MAAM,EAAE,aAAa,EAAE,GAAG,mBAAmB,CAAC,OAAO,CAAC,UAAU,CAAC;QACjE,MAAM,UAAU,GAAG,MAAM,IAAA,gDAAqB,EAAC,aAAa,CAAC,CAAC;QAC9D,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE,GAAG,MAAM,IAAA,0DAAyB,EACtD,aAAa,EACb,mBAAmB,CAAC,OAAO,EAC3B,UAAU,CACX,CAAC;YACF,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;QAC1C,CAAC;QACD,MAAM,IAAI,GAAG,MAAM,IAAA,kCAAc,EAAC,aAAa,CAAC,CAAC;QACjD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtB,MAAM,SAAS,GAAG,MAAM,aAAa,CAAC,YAAY,CAChD,IAAA,0CAAkB,EAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CACzD,CAAC;YACF,OAAO;gBACL,IAAI,EAAE,KAAK;gBACX,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC;aACvB,CAAC;QACJ,CAAC;QACD,MAAM,IAAI,uCAAiB,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,QAAQ,CACnB,mBAAoD,EACpD,aAAsB;QAEtB,IAAI,CAAC,mBAAmB;YAAE,OAAO,aAAa,CAAC;QAC/C,MAAM,EAAE,OAAO,EAAE,GAAG,mBAAmB,CAAC;QACxC,MAAM,aAAa,GAAG,IAAA,qCAAgB,EAAC,mBAAmB,CAAC,CAAC;QAC5D,IAAI,gBAAgB,CAAC;QACrB,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,GAAG,MAAM,sBAAsB,CAAC,uBAAuB,CACjF,aAAa,EACb,mBAAmB,CACpB,CAAC;YACF,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,mBAAmB,EAAE,QAAQ,CAAC,CAAC;YAC1D,MAAM,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,MAAM,aAAa,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACxF,IAAI,EAAE,IAAI,IAAA,uDAA0B,GAAE,CAAC,SAAS,CAAC,wCAAW,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC5E,gBAAgB,GAAG,MAAM,IAAA,gDAAqB,EAC5C,aAAa,EACb,mBAAmB,CAAC,OAAO,EAC3B,EAAE,CACH,CAAC;YACJ,CAAC;YAED,MAAM,MAAM,GAAG,CACb,MAAM,OAAO,CAAC,GAAG,CACf,aAAa;iBACV,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC;iBACjB,MAAM,CAAC,0CAAkB,CAAC;iBAC1B,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,aAAa,CAAC,qBAAqB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CACjE,CACF,CAAC,MAAM,CAAC,0CAAkB,CAAC,CAAC;YAC7B,OAAO;gBACL,IAAI;gBACJ,mBAAmB;gBACnB,QAAQ;gBACR,EAAE;gBACF,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,gBAAgB;aACjB,CAAC;QACJ,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,SAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACb,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;QACvC,CAAC;IACH,CAAC;IAED,UAAU;QACR,oEAAoE;QACpE,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,aAAc,CAAC,CAAC;QAC1C,oEAAoE;QACpE,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAqB,CAAC,CAAC;IACnD,CAAC;IAED,OAAO;QACL,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,CAAC;IACtC,CAAC;CACF;AA5JD,wDA4JC;AAEY,QAAA,sBAAsB,GAAG,IAAI,sBAAsB,EAAE,CAAC"}