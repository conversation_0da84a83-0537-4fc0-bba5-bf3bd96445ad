{"version": 3, "file": "code_suggestions_promo.test.js", "sourceRoot": "", "sources": ["../../../src/desktop/code_suggestions_promo.test.ts"], "names": [], "mappings": ";;;;;AAAA,oDAA4B;AAC5B,oGAA+F;AAE/F,qEAAqE;AACrE,2CAA+D;AAC/D,gEAA4D;AAE5D,IAAI,CAAC,IAAI,CAAC,4BAA4B,EAAE,GAAG,EAAE,CAAC,CAAC;IAC7C,cAAc,EAAE;QACd,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;YAC1B,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;SACnB,CAAC,CAAC;QACH,cAAc,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;KAClC;CACF,CAAC,CAAC,CAAC;AAEJ,MAAM,mBAAmB,GAAiB;IACxC,IAAI,EAAE,OAAO;IACb,WAAW,EAAE,oBAAoB;IACjC,QAAQ,EAAE,MAAM;IAChB,EAAE,EAAE,GAAG;IACP,KAAK,EAAE,EAAE;CACV,CAAC;AAEF,MAAM,2BAA2B,GAAiB;IAChD,GAAG,mBAAmB;IACtB,WAAW,EAAE,yBAAyB;CACvC,CAAC;AAEF,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;IACtC,IAAI,OAAgC,CAAC;IACrC,MAAM,yBAAyB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;IAE5C,UAAU,CAAC,GAAG,EAAE;QACd,OAAO,GAAG;YACR,WAAW,EAAE,IAAI,mCAAe,EAAE;SACG,CAAC;QAExC,IAAI;aACD,MAAM,CAAC,gBAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC;aAChD,eAAe,CAAC,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mEAAmE,EAAE,KAAK,IAAI,EAAE;QACjF,MAAM,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,4CAAgC,EAAE,IAAI,CAAC,CAAC;QACzE,IAAA,kDAAyB,EAAC,OAAO,CAAC,CAAC;QAEnC,MAAM,CAAC,gCAAc,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QAC1D,MAAM,CAAC,gBAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;IAC1E,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sCAAsC,EAAE,GAAG,EAAE;QACpD,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,gCAAc,CAAC,cAAc,CAAC,CAAC,mBAAmB,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC;YACtF,IAAA,kDAAyB,EAAC,OAAO,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;YACtF,QAAQ,CAAC,EAAoC,CAAC,CAAC;YAE/C,MAAM,CAAC,gBAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;YACtF,IAAI,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC,SAAS,EAAE,CAAC;YAEnD,MAAM,QAAQ,CAAC,EAAoC,CAAC,CAAC;YACrD,MAAM,CAAC,yBAAyB,CAAC,CAAC,gBAAgB,EAAE,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0CAA0C,EAAE,GAAG,EAAE;QACxD,UAAU,CAAC,GAAG,EAAE;YACd,IAAA,kDAAyB,EAAC,OAAO,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,gCAAc,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;YACxE,IAAI,CAAC,MAAM,CAAC,gCAAc,CAAC,cAAc,CAAC,CAAC,mBAAmB,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC;YACtF,QAAQ,EAAE,CAAC;YAEX,MAAM,CAAC,gBAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sEAAsE,EAAE,GAAG,EAAE;YAC9E,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,gCAAc,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;YACxE,IAAI,CAAC,MAAM,CAAC,gCAAc,CAAC,cAAc,CAAC,CAAC,mBAAmB,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAAC;YAC9F,QAAQ,EAAE,CAAC;YAEX,MAAM,CAAC,gBAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QACtE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}