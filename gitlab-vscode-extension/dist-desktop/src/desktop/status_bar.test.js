"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const vscode = __importStar(require("vscode"));
const entities_1 = require("../common/test_utils/entities");
const create_fake_partial_1 = require("../common/test_utils/create_fake_partial");
const entities_2 = require("./test_utils/entities");
const command_names_1 = require("./command_names");
jest.mock('./git/git_extension_wrapper');
jest.mock('./extension_state');
jest.mocked(vscode.workspace.getConfiguration).mockReturnValue((0, create_fake_partial_1.createFakePartial)({
    get: jest.fn(),
    showStatusBarLinks: true,
    showIssueLinkOnStatusBar: true,
    showMrStatusOnStatusBar: true,
}));
// StatusBar needs to be imported after we mock the configuration because it uses the configuration
// during module initialization
// eslint-disable-next-line import/first
const status_bar_1 = require("./status_bar");
const createFakeItem = () => ({
    show: jest.fn(),
    hide: jest.fn(),
    dispose: jest.fn(),
});
const createBranchInfo = (partialInfo = {}) => ({
    type: 'branch',
    projectInRepository: { project: entities_1.project, pointer: { repository: {} } },
    issues: [],
    jobs: [],
    userInitiated: true,
    ...partialInfo,
});
describe('status_bar', () => {
    let fakeItems;
    let statusBar;
    const getPipelineItem = () => fakeItems[0];
    const getMrItem = () => fakeItems[1];
    const getClosingIssueItem = () => fakeItems[2];
    beforeEach(() => {
        fakeItems = [];
        jest.mocked(vscode.window.createStatusBarItem).mockImplementation(() => {
            const fakeItem = createFakeItem();
            fakeItems.push(fakeItem);
            return fakeItem;
        });
        statusBar = new status_bar_1.StatusBar();
        statusBar.init();
    });
    afterEach(() => {
        statusBar.dispose();
    });
    it('hides all items when the state is not valid', async () => {
        await statusBar.refresh({ type: 'invalid' });
        expect(getPipelineItem().hide).toHaveBeenCalled();
        expect(getMrItem().hide).toHaveBeenCalled();
        expect(getClosingIssueItem().hide).toHaveBeenCalled();
    });
    describe('pipeline item', () => {
        it('initializes the pipeline item with success', async () => {
            await statusBar.refresh(createBranchInfo({ pipeline: entities_2.pipeline }));
            expect(getPipelineItem().show).toHaveBeenCalled();
            expect(getPipelineItem().hide).not.toHaveBeenCalled();
            expect(getPipelineItem().text).toBe('$(check) Pipeline passed');
        });
        it('prints jobs for running pipeline', async () => {
            const jobs = [
                {
                    ...entities_2.job,
                    status: 'running',
                    name: 'Unit Tests',
                },
                {
                    ...entities_2.job,
                    status: 'running',
                    name: 'Integration Tests',
                },
                {
                    ...entities_2.job,
                    status: 'success',
                    name: 'Lint',
                },
            ];
            await statusBar.refresh(createBranchInfo({ pipeline: { ...entities_2.pipeline, status: 'running' }, jobs }));
            expect(getPipelineItem().text).toBe('$(pulse) Pipeline running');
        });
        it('sorts by created time (starts with newer) and deduplicates jobs for running pipeline', async () => {
            const jobs = [
                {
                    ...entities_2.job,
                    status: 'running',
                    name: 'Integration Tests',
                    created_at: '2021-07-19T12:00:00.000Z',
                },
                {
                    ...entities_2.job,
                    status: 'running',
                    name: 'Unit Tests',
                    created_at: '2021-07-19T10:00:00.000Z',
                },
                {
                    ...entities_2.job,
                    status: 'running',
                    name: 'Unit Tests',
                    created_at: '2021-07-19T11:00:00.000Z',
                },
            ];
            await statusBar.refresh(createBranchInfo({ pipeline: { ...entities_2.pipeline, status: 'running' }, jobs }));
            expect(getPipelineItem().text).toBe('$(pulse) Pipeline running');
        });
        it('shows no pipeline text when there is no pipeline', async () => {
            await statusBar.refresh(createBranchInfo());
            expect(getPipelineItem().text).toBe('No pipeline');
        });
        it.each `
      status        | itemText
      ${'running'}  | ${'$(pulse) Pipeline running'}
      ${'success'}  | ${'$(check) Pipeline passed'}
      ${'pending'}  | ${'$(clock) Pipeline pending'}
      ${'failed'}   | ${'$(x) Pipeline failed'}
      ${'canceled'} | ${'$(circle-slash) Pipeline canceled'}
      ${'skipped'}  | ${'$(diff-renamed) Pipeline skipped'}
    `('shows $itemText for pipeline with status $status', async ({ status, itemText }) => {
            await statusBar.refresh(createBranchInfo({ pipeline: { ...entities_2.pipeline, status } }));
            expect(getPipelineItem().text).toBe(itemText);
        });
    });
    describe('MR item', () => {
        it('shows MR item', async () => {
            await statusBar.refresh(createBranchInfo({ mr: entities_2.mr }));
            expect(getMrItem().show).toHaveBeenCalled();
            expect(getMrItem().hide).not.toHaveBeenCalled();
            expect(getMrItem().text).toBe('$(git-pull-request) !2000');
            const command = getMrItem().command;
            expect(command.command).toBe('gl.showRichContent');
            expect(command.arguments?.[0]).toEqual(entities_2.mr);
        });
        it('shows create MR text when there is no MR', async () => {
            await statusBar.refresh(createBranchInfo());
            expect(getMrItem().text).toBe('$(git-pull-request) Create MR');
            expect(getMrItem().command).toBe(command_names_1.USER_COMMANDS.OPEN_CREATE_NEW_MR);
        });
    });
    describe('MR closing issue item', () => {
        it('shows closing issue for an MR', async () => {
            await statusBar.refresh(createBranchInfo({ mr: entities_2.mr, issues: [entities_2.issue] }));
            expect(getClosingIssueItem().show).toHaveBeenCalled();
            expect(getClosingIssueItem().hide).not.toHaveBeenCalled();
            expect(getClosingIssueItem().text).toBe('$(code) #1000');
            const command = getClosingIssueItem().command;
            expect(command.command).toBe('gl.showRichContent');
            expect(command.arguments?.[0]).toEqual(entities_2.issue);
        });
        it('shows no issue when there is not a closing issue', async () => {
            await statusBar.refresh(createBranchInfo({ mr: entities_2.mr, issues: [] }));
            expect(getClosingIssueItem().text).toBe('$(code) No issue');
            expect(getClosingIssueItem().command).toBe(undefined);
        });
        it('hides the item when there is is no MR', async () => {
            await statusBar.refresh(createBranchInfo());
            expect(getClosingIssueItem().hide).toHaveBeenCalled();
        });
    });
});
//# sourceMappingURL=status_bar.test.js.map