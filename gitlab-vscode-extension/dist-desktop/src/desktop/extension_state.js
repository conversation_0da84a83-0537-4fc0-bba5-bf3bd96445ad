"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getExtensionStateSingleton = exports.setExtensionStateSingleton = exports.ExtensionState = void 0;
const vscode = __importStar(require("vscode"));
const extension_configuration_service_1 = require("../common/utils/extension_configuration_service");
const git_extension_wrapper_1 = require("./git/git_extension_wrapper");
const workspace_account_manager_1 = require("./accounts/workspace_account_manager");
const CONTEXT_REMOTE_ENV = 'gitlab:isRemoteEnvironment';
const CONTEXT_NO_ACCOUNT = 'gitlab:noAccount';
const CONTEXT_OPEN_REPO_COUNT = 'gitlab:openRepositoryCount';
const CONTEXT_VALID_STATE = 'gitlab:validState';
const CONTEXT_SHOULD_SELECT_ACCOUNT = 'gitlab:shouldSelectAccount';
const openRepositoryCount = () => git_extension_wrapper_1.gitExtensionWrapper.gitRepositories.length;
const setContext = (name, value) => vscode.commands.executeCommand('setContext', name, value);
class ExtensionState {
    #changeValidEmitter = new vscode.EventEmitter();
    onDidChangeValid = this.#changeValidEmitter.event;
    #workspaceAccountManager;
    #lastValid = false;
    constructor(workspaceAccountManager) {
        this.#workspaceAccountManager = workspaceAccountManager;
        workspaceAccountManager.onChange(this.#updateExtensionStatus, this);
        extension_configuration_service_1.extensionConfigurationService.onChange(this.#updateExtensionStatus, this);
        this.#lastValid = this.isValid();
    }
    async init() {
        git_extension_wrapper_1.gitExtensionWrapper.onRepositoryCountChanged(this.#updateExtensionStatus, this);
        await this.#updateExtensionStatus();
    }
    #hasAnyAccounts() {
        return this.#workspaceAccountManager.state.type !== workspace_account_manager_1.NO_ACCOUNTS;
    }
    isValid() {
        return this.#hasAnyAccounts() && openRepositoryCount() > 0 && !this.#shouldSelectAccount();
    }
    #shouldSelectAccount() {
        return this.#workspaceAccountManager.state.type === workspace_account_manager_1.MULTIPLE_AVAILABLE_ACCOUNTS;
    }
    async #updateExtensionStatus() {
        await setContext(CONTEXT_REMOTE_ENV, Boolean(vscode.env.remoteName));
        await setContext(CONTEXT_NO_ACCOUNT, !this.#hasAnyAccounts());
        await setContext(CONTEXT_OPEN_REPO_COUNT, openRepositoryCount());
        await setContext(CONTEXT_SHOULD_SELECT_ACCOUNT, this.#shouldSelectAccount());
        await setContext(CONTEXT_VALID_STATE, this.isValid());
        if (this.#lastValid !== this.isValid()) {
            this.#lastValid = this.isValid();
            this.#changeValidEmitter.fire();
        }
    }
}
exports.ExtensionState = ExtensionState;
let extensionState;
const setExtensionStateSingleton = (state) => {
    extensionState = state;
};
exports.setExtensionStateSingleton = setExtensionStateSingleton;
/* @deprecated Try to pass in the ExtensionState in the constructor rather than using this singleton */
const getExtensionStateSingleton = () => {
    if (!extensionState)
        throw new Error('ExtensionState has not been initialized.');
    return extensionState;
};
exports.getExtensionStateSingleton = getExtensionStateSingleton;
//# sourceMappingURL=extension_state.js.map