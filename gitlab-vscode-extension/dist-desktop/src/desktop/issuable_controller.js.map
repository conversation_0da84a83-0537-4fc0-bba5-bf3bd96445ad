{"version": 3, "file": "issuable_controller.js", "sourceRoot": "", "sources": ["../../../src/desktop/issuable_controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAA6B;AAC7B,oDAA4B;AAC5B,+CAAiC;AACjC,uCAAoC;AACpC,gEAA4D;AAC5D,gFAA2E;AAC3E,4FAAuF;AACvF,yCAAqC;AACrC,+EAAyE;AAEzE,kFAA0E;AAE1E,oEAA+D;AAC/D,2CAA+C;AAO/C,KAAK,UAAU,iBAAiB,CAC9B,KAA0B,EAC1B,QAAsB,EACtB,aAA4B;IAE5B,IAAI,CAAC,KAAK,CAAC,MAAM;QAAE,OAAO;IAE1B,MAAM,WAAW,GAAG,IAAA,iCAAc,EAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAElD,MAAM,oBAAoB,GAAG,MAAM,aAAa;SAC7C,4BAA4B,CAAC,QAAQ,CAAC;SACtC,KAAK,CAAC,CAAC,CAAC,EAAE;QACT,IAAA,0BAAW,EAAC,CAAC,CAAC,CAAC;QACf,OAAO,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC;IAEL,MAAM,WAAW,CAAC;IAClB,MAAM,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;QAC9B,IAAI,EAAE,eAAe;QACrB,QAAQ;QACR,WAAW,EAAE,oBAAoB;KAClC,CAAC,CAAC;AACL,CAAC;AAED,MAAM,kBAAkB;IACtB,OAAO,CAA2B;IAElC,YAAY,GAAqD,EAAE,CAAC;IAEpE,IAAI,CAAC,OAAgC;QACnC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED,YAAY,CAAC,QAAsB;QACjC,IAAA,gBAAM,EAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrB,MAAM,KAAK,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC;QAElD,OAAO,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,4BAAgB,EAAE,KAAK,EAAE,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE;YACtF,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE;gBAClB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;gBAClE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;aACjE;YACD,uBAAuB,EAAE,IAAI;SAC9B,CAAyB,CAAC;IAC7B,CAAC;IAED,qBAAqB,GACnB,CACE,KAA0B,EAC1B,QAAsB,EACtB,mBAAwC,EACxC,EAAE;IACJ,8BAA8B;IAC9B,8DAA8D;IAC9D,KAAK,EAAE,OAAY,EAAE,EAAE;QACrB,IAAI,OAAO,CAAC,OAAO,KAAK,gBAAgB,EAAE,CAAC;YACzC,IAAI,QAAQ,GAAG,MAAM,IAAA,qCAAgB,EAAC,mBAAmB,CAAC,CAAC,cAAc,CACvE,OAAO,CAAC,QAAQ,EAChB,mBAAmB,CAAC,OAAO,CAC5B,CAAC;YACF,QAAQ,GAAG,IAAA,gDAAqB,EAAC,QAAQ,IAAI,EAAE,EAAE,mBAAmB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAE1F,MAAM,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC9B,IAAI,EAAE,kBAAkB;gBACxB,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,QAAQ,EAAE,QAAQ;aACnB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,OAAO,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC;YACnC,IAAI,CAAC;gBACH,MAAM,aAAa,GAAG,IAAA,qCAAgB,EAAC,mBAAmB,CAAC,CAAC;gBAC5D,IAAI,CAAC;oBACH,MAAM,aAAa,CAAC,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;gBAC1E,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,yEAAyE;oBACzE,MAAM,kBAAkB,GAAG,MAAM,CAC/B,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,UAAU,CAAC,eAAe,CAAC,CAC1D,CAAC;oBACF,IAAI,CAAC,kBAAkB,EAAE,CAAC;wBACxB,MAAM,KAAK,CAAC;oBACd,CAAC;gBACH,CAAC;gBACD,MAAM,oBAAoB,GAAG,MAAM,aAAa,CAAC,4BAA4B,CAAC,QAAQ,CAAC,CAAC;gBACxF,MAAM,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC9B,IAAI,EAAE,eAAe;oBACrB,QAAQ;oBACR,WAAW,EAAE,oBAAoB;iBAClC,CAAC,CAAC;gBACH,MAAM,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;YACzD,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,SAAG,CAAC,KAAK,CAAC,mCAAmC,EAAE,CAAC,CAAC,CAAC;gBAClD,MAAM,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;IACH,CAAC,CAAC;IAEJ,uBAAuB,CAAC,QAAsB;QAC5C,MAAM,UAAU,GAAG,CAAC,KAAa,EAAE,IAAY,EAAE,EAAE,CACjD,MAAM,CAAC,GAAG,CAAC,IAAI,CACb,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,IAAI,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CACrF,CAAC;QACJ,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QACzD,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;QAC9D,MAAM,aAAa,GAAG,UAAU,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QACvD,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAAC;QAC5D,OAAO,IAAA,YAAI,EAAC,QAAQ,CAAC;YACnB,CAAC,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,UAAU,EAAE;YAC1C,CAAC,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;IACrD,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,QAAsB,EAAE,cAAsB;QACvD,MAAM,QAAQ,GAAG,GAAG,cAAc,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;QACpD,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAChD,IAAI,WAAW,EAAE,CAAC;YAChB,WAAW,CAAC,MAAM,EAAE,CAAC;YACrB,OAAO,WAAW,CAAC;QACrB,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;QAC9D,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;QACvC,QAAQ,CAAC,YAAY,CAAC,GAAG,EAAE;YACzB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC;QAC1C,CAAC,CAAC,CAAC;QACH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,QAAsB,EAAE,cAAsB;QAC1D,IAAA,gBAAM,EAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrB,MAAM,mBAAmB,GAAG,IAAA,gDAAoB,GAAE,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;QAEpF,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAC1C,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,IAAA,6CAAoB,EAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QACzF,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;QACxD,KAAK,CAAC,cAAc,GAAG,cAAc,CAAC;QAEtC,MAAM,iBAAiB,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAA,qCAAgB,EAAC,mBAAmB,CAAC,CAAC,CAAC;QAChF,KAAK,CAAC,oBAAoB,CAAC,KAAK,IAAI,EAAE;YACpC,MAAM,iBAAiB,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAA,qCAAgB,EAAC,mBAAmB,CAAC,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAC/B,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,QAAQ,EAAE,mBAAmB,CAAC,CACjE,CAAC;QACF,OAAO,KAAK,CAAC;IACf,CAAC;IAED,eAAe,CAAC,QAAgB;QAC9B,OAAO,QAAQ,KAAK,qBAAqB,4BAAgB,EAAE,CAAC;IAC9D,CAAC;IAED,IAAI,0BAA0B;QAC5B,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QACpE,OAAO,KAAK,EAAE,cAAc,CAAC;IAC/B,CAAC;CACF;AAEY,QAAA,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAC"}