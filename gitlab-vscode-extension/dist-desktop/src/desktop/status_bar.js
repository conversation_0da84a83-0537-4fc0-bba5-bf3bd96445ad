"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.statusBar = exports.StatusBar = void 0;
const assert = require("assert");
const vscode = __importStar(require("vscode"));
const status_bar_item_1 = require("../common/utils/status_bar_item");
const openers = __importStar(require("./commands/openers"));
const command_names_1 = require("./command_names");
const current_branch_refresher_1 = require("./current_branch_refresher");
// FIXME: if you are touching this configuration statement, move the configuration to extension_configuration.ts
const { showStatusBarLinks, showIssueLinkOnStatusBar, showMrStatusOnStatusBar, showPipelineUpdateNotifications, } = vscode.workspace.getConfiguration('gitlab');
const iconForStatus = {
    running: { icon: 'pulse' },
    pending: { icon: 'clock' },
    success: { icon: 'check', text: 'passed' },
    failed: { icon: 'x' },
    canceling: { icon: 'circle-slash' },
    canceled: { icon: 'circle-slash' },
    skipped: { icon: 'diff-renamed' },
};
const getStatusText = (status) => iconForStatus[status]?.text || status;
const openIssuableInWebview = (issuable, rootFsPath) => ({
    title: '',
    command: command_names_1.PROGRAMMATIC_COMMANDS.SHOW_RICH_CONTENT,
    arguments: [issuable, rootFsPath],
});
class StatusBar {
    pipelineStatusBarItem;
    mrStatusBarItem;
    mrIssueStatusBarItem;
    #refreshSubscription;
    firstRun = true;
    async refresh(state) {
        if (state.type === 'branch') {
            const { rootFsPath } = state.projectInRepository.pointer.repository;
            await this.updatePipelineItem(state.pipeline, state.jobs, state.projectInRepository);
            this.updateMrItem(state.mr, rootFsPath);
            this.fetchMrClosingIssue(state.mr, state.issues, rootFsPath);
        }
        else if (state.type === 'tag') {
            await this.updatePipelineItem(state.pipeline, state.jobs, state.projectInRepository);
            this.mrStatusBarItem?.hide();
            this.mrIssueStatusBarItem?.hide();
        }
        else {
            this.hideAllItems();
        }
    }
    hideAllItems() {
        this.pipelineStatusBarItem?.hide();
        this.mrStatusBarItem?.hide();
        this.mrIssueStatusBarItem?.hide();
    }
    async updatePipelineItem(pipeline, jobs, projectInRepository) {
        if (!this.pipelineStatusBarItem)
            return;
        if (!pipeline) {
            this.pipelineStatusBarItem.text = 'No pipeline';
            this.pipelineStatusBarItem.show();
            this.firstRun = false;
            return;
        }
        const { status } = pipeline;
        const statusText = getStatusText(status);
        const msg = `$(${iconForStatus[status]?.icon}) Pipeline ${statusText}`;
        if (showPipelineUpdateNotifications &&
            this.pipelineStatusBarItem.text !== msg &&
            !this.firstRun) {
            const message = `Pipeline ${statusText}`;
            await vscode.window
                .showInformationMessage(message, { modal: false }, 'View in GitLab')
                .then(async (selection) => {
                if (selection === 'View in GitLab') {
                    await openers.openCurrentPipeline(projectInRepository);
                }
                return undefined;
            });
        }
        this.pipelineStatusBarItem.text = msg;
        this.pipelineStatusBarItem.show();
        this.firstRun = false;
    }
    fetchMrClosingIssue(mr, closingIssues, rootFsPath) {
        if (!this.mrIssueStatusBarItem)
            return;
        if (mr) {
            let text = `$(code) No issue`;
            let command;
            const firstIssue = closingIssues[0];
            if (firstIssue) {
                text = `$(code) #${firstIssue.iid}`;
                command = openIssuableInWebview(firstIssue, rootFsPath);
            }
            this.mrIssueStatusBarItem.text = text;
            this.mrIssueStatusBarItem.command = command;
            this.mrIssueStatusBarItem.show();
        }
        else {
            this.mrIssueStatusBarItem.hide();
        }
    }
    updateMrItem(mr, rootFsPath) {
        if (!this.mrStatusBarItem)
            return;
        this.mrStatusBarItem.show();
        this.mrStatusBarItem.command = mr
            ? openIssuableInWebview(mr, rootFsPath)
            : command_names_1.USER_COMMANDS.OPEN_CREATE_NEW_MR;
        this.mrStatusBarItem.text = mr
            ? `$(git-pull-request) !${mr.iid}`
            : '$(git-pull-request) Create MR';
    }
    init() {
        assert(!this.pipelineStatusBarItem, 'The status bar is already initialized');
        if (showStatusBarLinks) {
            this.#refreshSubscription = current_branch_refresher_1.currentBranchRefresher.onStateChanged(e => this.refresh(e));
            this.pipelineStatusBarItem = (0, status_bar_item_1.createStatusBarItem)({
                priority: 2,
                id: 'gl.status.pipeline',
                name: 'GitLab Workflow: Pipeline',
                initialText: '$(info) Fetching pipeline...',
                command: command_names_1.USER_COMMANDS.PIPELINE_ACTIONS,
                alignment: vscode.StatusBarAlignment.Left,
            });
            if (showMrStatusOnStatusBar) {
                this.mrStatusBarItem = (0, status_bar_item_1.createStatusBarItem)({
                    priority: 1,
                    id: 'gl.status.mr',
                    name: 'GitLab Workflow: Merge Request',
                    initialText: '$(info) Finding MR...',
                    alignment: vscode.StatusBarAlignment.Left,
                });
                if (showIssueLinkOnStatusBar) {
                    this.mrIssueStatusBarItem = (0, status_bar_item_1.createStatusBarItem)({
                        priority: 0,
                        id: 'gl.status.issue',
                        name: 'GitLab Workflow: Issue',
                        initialText: '$(info) Fetching closing issue...',
                        alignment: vscode.StatusBarAlignment.Left,
                    });
                }
            }
        }
    }
    dispose() {
        if (showStatusBarLinks) {
            this.#refreshSubscription?.dispose();
            this.pipelineStatusBarItem?.dispose();
            if (showIssueLinkOnStatusBar) {
                this.mrIssueStatusBarItem?.dispose();
            }
            if (showMrStatusOnStatusBar) {
                this.mrStatusBarItem?.dispose();
            }
        }
    }
}
exports.StatusBar = StatusBar;
exports.statusBar = new StatusBar();
//# sourceMappingURL=status_bar.js.map