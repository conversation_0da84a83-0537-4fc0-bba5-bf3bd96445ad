"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const create_fake_partial_1 = require("../common/test_utils/create_fake_partial");
const extension_configuration_service_1 = require("../common/utils/extension_configuration_service");
const current_branch_refresher_1 = require("./current_branch_refresher");
const entities_1 = require("./test_utils/entities");
const get_gitlab_service_1 = require("./gitlab/get_gitlab_service");
const get_tracking_branch_name_1 = require("./git/get_tracking_branch_name");
const get_tags_for_head_1 = require("./git/get_tags_for_head");
const get_pipeline_and_mr_for_branch_1 = require("./gitlab/get_pipeline_and_mr_for_branch");
const get_all_security_reports_1 = require("./gitlab/security_findings/get_all_security_reports");
jest.mock('../common/utils/extension_configuration');
jest.mock('./gitlab/get_gitlab_service');
jest.mock('./git/get_tracking_branch_name');
jest.mock('./git/get_tags_for_head');
jest.mock('./gitlab/get_pipeline_and_mr_for_branch');
jest.mock('./gitlab/security_findings/get_all_security_reports');
describe('CurrentBranchRefrehser', () => {
    beforeEach(() => {
        jest
            .spyOn(extension_configuration_service_1.extensionConfigurationService, 'getConfiguration')
            .mockReturnValue((0, create_fake_partial_1.createFakePartial)({ featureFlags: {} }));
    });
    describe('invalid state', () => {
        it('returns invalid state if the current repo does not contain GitLab project', async () => {
            const state = await current_branch_refresher_1.CurrentBranchRefresher.getState(undefined, false);
            expect(state.type).toBe('invalid');
        });
        it('returns invalid state if fetching the mr and pipelines fails', async () => {
            jest.mocked(get_tracking_branch_name_1.getTrackingBranchName).mockResolvedValue('branch');
            const state = await current_branch_refresher_1.CurrentBranchRefresher.getState(entities_1.projectInRepository, false);
            expect(state.type).toBe('invalid');
        });
    });
    describe('valid state', () => {
        beforeEach(() => {
            jest.mocked(get_gitlab_service_1.getGitLabService).mockReturnValue((0, create_fake_partial_1.createFakePartial)({
                getMrClosingIssues: async () => [(0, create_fake_partial_1.createFakePartial)({ iid: 123 })],
                getSingleProjectIssue: async () => entities_1.issue,
                getJobsForPipeline: async () => [entities_1.job],
                getTriggerJobsForPipeline: async () => [],
                getExternalStatusForCommit: async () => [entities_1.externalStatus],
                fetchFromApi: async () => [entities_1.pipeline],
            }));
            jest.mocked(get_pipeline_and_mr_for_branch_1.getPipelineAndMrForBranch).mockResolvedValue({ pipeline: entities_1.pipeline, mr: entities_1.mr });
        });
        it('returns valid state if GitLab service returns pipeline and mr', async () => {
            jest.mocked(get_tracking_branch_name_1.getTrackingBranchName).mockResolvedValue('branch');
            const state = await current_branch_refresher_1.CurrentBranchRefresher.getState(entities_1.projectInRepository, false);
            const branchState = state;
            expect(branchState.pipeline).toEqual(entities_1.pipeline);
            expect(branchState.mr).toEqual(entities_1.mr);
            expect(branchState.issues).toEqual([entities_1.issue]);
            expect(branchState.securityFindings).toEqual(undefined);
        });
        it('returns valid state if GitLab service returns pipeline and mr and security scans', async () => {
            jest.mocked(get_tracking_branch_name_1.getTrackingBranchName).mockResolvedValue('branch');
            jest.mocked(get_all_security_reports_1.getAllSecurityReports).mockResolvedValue(entities_1.securityReportComparer);
            const state = await current_branch_refresher_1.CurrentBranchRefresher.getState(entities_1.projectInRepository, false);
            const branchState = state;
            expect(branchState.pipeline).toEqual(entities_1.pipeline);
            expect(branchState.mr).toEqual(entities_1.mr);
            expect(branchState.issues).toEqual([entities_1.issue]);
            expect(branchState.securityFindings).toEqual(entities_1.securityReportComparer);
        });
        it('returns valid state if repository has checked out a tag', async () => {
            jest.mocked(get_tracking_branch_name_1.getTrackingBranchName).mockResolvedValue(undefined);
            jest.mocked(get_tags_for_head_1.getTagsForHead).mockResolvedValue(['tag1']);
            const state = await current_branch_refresher_1.CurrentBranchRefresher.getState(entities_1.projectInRepository, false);
            expect(state.type).toBe('tag');
            expect(state.pipeline).toEqual(entities_1.pipeline);
        });
        it('returns pipeline jobs and external statuses', async () => {
            jest.mocked(get_tracking_branch_name_1.getTrackingBranchName).mockResolvedValue('branch');
            const state = await current_branch_refresher_1.CurrentBranchRefresher.getState(entities_1.projectInRepository, false);
            expect(state.type).toBe('branch');
            expect(state.jobs).toEqual([entities_1.job, entities_1.externalStatus]);
        });
    });
});
//# sourceMappingURL=current_branch_refresher.test.js.map