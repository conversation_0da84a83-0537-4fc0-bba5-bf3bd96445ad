{"version": 3, "file": "status_bar.js", "sourceRoot": "", "sources": ["../../../src/desktop/status_bar.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iCAAkC;AAClC,+CAAiC;AACjC,qEAAsE;AACtE,4DAA8C;AAC9C,mDAAuE;AACvE,yEAA+E;AAG/E,gHAAgH;AAChH,MAAM,EACJ,kBAAkB,EAClB,wBAAwB,EACxB,uBAAuB,EACvB,+BAA+B,GAChC,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AAEhD,MAAM,aAAa,GAAgE;IACjF,OAAO,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;IAC1B,OAAO,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;IAC1B,OAAO,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE;IAC1C,MAAM,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;IACrB,SAAS,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE;IACnC,QAAQ,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE;IAClC,OAAO,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE;CAClC,CAAC;AAEF,MAAM,aAAa,GAAG,CAAC,MAAc,EAAE,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,IAAI,IAAI,MAAM,CAAC;AAEhF,MAAM,qBAAqB,GAAG,CAAC,QAAsB,EAAE,UAAkB,EAAkB,EAAE,CAAC,CAAC;IAC7F,KAAK,EAAE,EAAE;IACT,OAAO,EAAE,qCAAqB,CAAC,iBAAiB;IAChD,SAAS,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;CAClC,CAAC,CAAC;AAEH,MAAa,SAAS;IACpB,qBAAqB,CAAwB;IAE7C,eAAe,CAAwB;IAEvC,oBAAoB,CAAwB;IAE5C,oBAAoB,CAAqB;IAEzC,QAAQ,GAAG,IAAI,CAAC;IAEhB,KAAK,CAAC,OAAO,CAAC,KAAgB;QAC5B,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC5B,MAAM,EAAE,UAAU,EAAE,GAAG,KAAK,CAAC,mBAAmB,CAAC,OAAO,CAAC,UAAU,CAAC;YACpE,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACrF,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;YACxC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAC/D,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;YAChC,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACrF,IAAI,CAAC,eAAe,EAAE,IAAI,EAAE,CAAC;YAC7B,IAAI,CAAC,oBAAoB,EAAE,IAAI,EAAE,CAAC;QACpC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC;IACH,CAAC;IAED,YAAY;QACV,IAAI,CAAC,qBAAqB,EAAE,IAAI,EAAE,CAAC;QACnC,IAAI,CAAC,eAAe,EAAE,IAAI,EAAE,CAAC;QAC7B,IAAI,CAAC,oBAAoB,EAAE,IAAI,EAAE,CAAC;IACpC,CAAC;IAED,KAAK,CAAC,kBAAkB,CACtB,QAAkC,EAClC,IAAe,EACf,mBAAwC;QAExC,IAAI,CAAC,IAAI,CAAC,qBAAqB;YAAE,OAAO;QACxC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,CAAC,qBAAqB,CAAC,IAAI,GAAG,aAAa,CAAC;YAChD,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC;YAClC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YACtB,OAAO;QACT,CAAC;QACD,MAAM,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC;QAC5B,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;QAEzC,MAAM,GAAG,GAAG,KAAK,aAAa,CAAC,MAAM,CAAC,EAAE,IAAI,cAAc,UAAU,EAAE,CAAC;QAEvE,IACE,+BAA+B;YAC/B,IAAI,CAAC,qBAAqB,CAAC,IAAI,KAAK,GAAG;YACvC,CAAC,IAAI,CAAC,QAAQ,EACd,CAAC;YACD,MAAM,OAAO,GAAG,YAAY,UAAU,EAAE,CAAC;YAEzC,MAAM,MAAM,CAAC,MAAM;iBAChB,sBAAsB,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,gBAAgB,CAAC;iBACnE,IAAI,CAAC,KAAK,EAAC,SAAS,EAAC,EAAE;gBACtB,IAAI,SAAS,KAAK,gBAAgB,EAAE,CAAC;oBACnC,MAAM,OAAO,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;gBACzD,CAAC;gBACD,OAAO,SAAS,CAAC;YACnB,CAAC,CAAC,CAAC;QACP,CAAC;QAED,IAAI,CAAC,qBAAqB,CAAC,IAAI,GAAG,GAAG,CAAC;QACtC,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxB,CAAC;IAED,mBAAmB,CACjB,EAAsB,EACtB,aAA6B,EAC7B,UAAkB;QAElB,IAAI,CAAC,IAAI,CAAC,oBAAoB;YAAE,OAAO;QACvC,IAAI,EAAE,EAAE,CAAC;YACP,IAAI,IAAI,GAAG,kBAAkB,CAAC;YAC9B,IAAI,OAAO,CAAC;YAEZ,MAAM,UAAU,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;YACpC,IAAI,UAAU,EAAE,CAAC;gBACf,IAAI,GAAG,YAAY,UAAU,CAAC,GAAG,EAAE,CAAC;gBACpC,OAAO,GAAG,qBAAqB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;YAC1D,CAAC;YAED,IAAI,CAAC,oBAAoB,CAAC,IAAI,GAAG,IAAI,CAAC;YACtC,IAAI,CAAC,oBAAoB,CAAC,OAAO,GAAG,OAAO,CAAC;YAC5C,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;QACnC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;QACnC,CAAC;IACH,CAAC;IAED,YAAY,CAAC,EAAsB,EAAE,UAAkB;QACrD,IAAI,CAAC,IAAI,CAAC,eAAe;YAAE,OAAO;QAClC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;QAC5B,IAAI,CAAC,eAAe,CAAC,OAAO,GAAG,EAAE;YAC/B,CAAC,CAAC,qBAAqB,CAAC,EAAE,EAAE,UAAU,CAAC;YACvC,CAAC,CAAC,6BAAa,CAAC,kBAAkB,CAAC;QACrC,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,EAAE;YAC5B,CAAC,CAAC,wBAAwB,EAAE,CAAC,GAAG,EAAE;YAClC,CAAC,CAAC,+BAA+B,CAAC;IACtC,CAAC;IAED,IAAI;QACF,MAAM,CAAC,CAAC,IAAI,CAAC,qBAAqB,EAAE,uCAAuC,CAAC,CAAC;QAC7E,IAAI,kBAAkB,EAAE,CAAC;YACvB,IAAI,CAAC,oBAAoB,GAAG,iDAAsB,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YACxF,IAAI,CAAC,qBAAqB,GAAG,IAAA,qCAAmB,EAAC;gBAC/C,QAAQ,EAAE,CAAC;gBACX,EAAE,EAAE,oBAAoB;gBACxB,IAAI,EAAE,2BAA2B;gBACjC,WAAW,EAAE,8BAA8B;gBAC3C,OAAO,EAAE,6BAAa,CAAC,gBAAgB;gBACvC,SAAS,EAAE,MAAM,CAAC,kBAAkB,CAAC,IAAI;aAC1C,CAAC,CAAC;YACH,IAAI,uBAAuB,EAAE,CAAC;gBAC5B,IAAI,CAAC,eAAe,GAAG,IAAA,qCAAmB,EAAC;oBACzC,QAAQ,EAAE,CAAC;oBACX,EAAE,EAAE,cAAc;oBAClB,IAAI,EAAE,gCAAgC;oBACtC,WAAW,EAAE,uBAAuB;oBACpC,SAAS,EAAE,MAAM,CAAC,kBAAkB,CAAC,IAAI;iBAC1C,CAAC,CAAC;gBACH,IAAI,wBAAwB,EAAE,CAAC;oBAC7B,IAAI,CAAC,oBAAoB,GAAG,IAAA,qCAAmB,EAAC;wBAC9C,QAAQ,EAAE,CAAC;wBACX,EAAE,EAAE,iBAAiB;wBACrB,IAAI,EAAE,wBAAwB;wBAC9B,WAAW,EAAE,mCAAmC;wBAChD,SAAS,EAAE,MAAM,CAAC,kBAAkB,CAAC,IAAI;qBAC1C,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO;QACL,IAAI,kBAAkB,EAAE,CAAC;YACvB,IAAI,CAAC,oBAAoB,EAAE,OAAO,EAAE,CAAC;YACrC,IAAI,CAAC,qBAAqB,EAAE,OAAO,EAAE,CAAC;YAEtC,IAAI,wBAAwB,EAAE,CAAC;gBAC7B,IAAI,CAAC,oBAAoB,EAAE,OAAO,EAAE,CAAC;YACvC,CAAC;YACD,IAAI,uBAAuB,EAAE,CAAC;gBAC5B,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE,CAAC;YAClC,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAxJD,8BAwJC;AAEY,QAAA,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC"}