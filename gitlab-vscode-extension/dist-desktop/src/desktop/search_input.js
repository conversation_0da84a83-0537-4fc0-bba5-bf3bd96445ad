"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.showAdvancedSearchInput = exports.showMergeRequestSearchInput = exports.showIssueSearchInput = void 0;
const vscode_1 = __importDefault(require("vscode"));
const create_query_string_1 = require("../common/utils/create_query_string");
const constants_1 = require("../common/constants");
const openers = __importStar(require("./commands/openers"));
const SELF_MANAGED_INSTANCE_SEARCH_SCOPES = {
    Code: 'blobs',
    Issues: 'issues',
    'Merge Requests': 'merge_requests',
    Wiki: 'wiki_blobs',
    Commits: 'commits',
    Comments: 'notes',
    Milestones: 'milestones',
    Users: 'users',
    Projects: 'projects',
};
const GITLAB_COM_SEARCH_SCOPES = {
    Issues: 'issues',
    'Merge Requests': 'merge_requests',
    Comments: 'notes',
    Milestones: 'milestones',
    Users: 'users',
    Projects: 'projects',
};
const ALL_PROJECT_SEARCH_SCOPES = {
    Code: 'blobs',
    Issues: 'issues',
    'Merge Requests': 'merge_requests',
    Wiki: 'wiki_blobs',
    Commits: 'commits',
    Comments: 'notes',
    Milestones: 'milestones',
    Users: 'users',
};
const PROJECT_SEARCH_LEVEL = {
    label: 'Project',
    searchLevel: 'project',
    description: 'The search includes only the current project in results',
};
const INSTANCE_SEARCH_LEVEL = {
    label: 'Instance',
    searchLevel: 'instance',
    description: 'The search includes all projects in results',
};
const parseQuery = (query, noteableType) => {
    // FIXME: specify correct type
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const params = {};
    const tokens = query
        .replace(/: /g, ':') // Normalize spaces after tokens.
        .replace(/\s[a-z]*:/gi, t => `\n${t}`) // Get tokens and add new line.
        .split('\n') // Create array from tokens.
        .map(t => t.trim().split(':')); // Return new array with token and value arrays.
    // If there is no token it's a basic text search.
    if (tokens.length === 1 && tokens[0][1] === undefined) {
        // eslint-disable-next-line prefer-destructuring
        params.search = tokens[0][0];
    }
    else {
        tokens.forEach(t => {
            const [token, value] = t;
            switch (token) {
                // Merge value of `labels` token with previous labels.
                // By doing this we will be able to use `labels` and `label` token together.
                case 'labels':
                    params.labels = (params.labels || []).concat(value.replace(/, /g, ',').split(','));
                    break;
                // Labels can be multiple and should be comma separated.
                case 'label':
                    params.labels = params.labels || [];
                    params.labels.push(value);
                    break;
                // GitLab requires Title and Description in `search` query param.
                // Since we are passing this as search query, GL will also search in issue descriptions too.
                case 'title':
                    params.search = value;
                    break;
                // GitLab UI requires milestone as milestone_title.
                case 'milestone':
                    delete params.milestone;
                    params.milestone_title = value;
                    break;
                // GitLab requires author name as author_username.
                // `author` is syntatic sugar of extension.
                case 'author':
                    delete params.author;
                    if (value === 'me') {
                        params.scope = 'created-by-me';
                    }
                    else {
                        params.author_username = value;
                    }
                    break;
                // GitLab requires assignee name as assignee_username[] for issues.
                // and as assignee_username for merge requests `assignee` is syntatic sugar of extension.
                // We currently don't support multiple assignees for issues.
                case 'assignee':
                    delete params.assignee;
                    if (value === 'me') {
                        params.scope = 'assigned-to-me';
                    }
                    else {
                        const key = noteableType === 'merge_requests' ? 'assignee_username' : 'assignee_username[]';
                        params[key] = value;
                    }
                    break;
                // Add other tokens. If there is a typo in token name GL either ignore it or won't find any issue.
                default:
                    params[token] = value;
                    break;
            }
        });
    }
    return (0, create_query_string_1.createQueryString)(params);
};
function getSearchScope(possibleSearchCopes) {
    return vscode_1.default.window
        .showQuickPick(Object.keys(possibleSearchCopes), { title: 'Search scope' })
        .then(item => (item ? possibleSearchCopes[item] : undefined));
}
function getSearchLevel() {
    return vscode_1.default.window
        .showQuickPick([PROJECT_SEARCH_LEVEL, INSTANCE_SEARCH_LEVEL], {
        title: 'Search this project or the whole GitLab instance?',
    })
        .then(item => item?.searchLevel);
}
async function showSearchInputFor(noteableType, project) {
    const query = await vscode_1.default.window.showInputBox({
        ignoreFocusOut: true,
        placeHolder: 'Search in title or description. (Check extension page for search with filters)',
    });
    if (!query)
        return;
    const queryString = parseQuery(query, noteableType);
    await openers.openUrl(`${project.webUrl}/${noteableType}${queryString}`);
}
const showIssueSearchInput = async (projectInRepository) => showSearchInputFor('issues', projectInRepository.project);
exports.showIssueSearchInput = showIssueSearchInput;
const showMergeRequestSearchInput = async (projectInRepository) => showSearchInputFor('merge_requests', projectInRepository.project);
exports.showMergeRequestSearchInput = showMergeRequestSearchInput;
const showAdvancedSearchInput = async (projectInRepository) => {
    const { instanceUrl } = projectInRepository.account;
    const { project } = projectInRepository;
    const query = await vscode_1.default.window.showInputBox({
        ignoreFocusOut: true,
        placeHolder: 'Advanced Search. (Check extension page for Advanced Search)',
    });
    if (!query)
        return;
    const searchLevel = await getSearchLevel();
    // Assume project search
    let possibleSearchScopes = ALL_PROJECT_SEARCH_SCOPES;
    if (searchLevel === 'instance') {
        possibleSearchScopes =
            instanceUrl === constants_1.GITLAB_COM_URL
                ? GITLAB_COM_SEARCH_SCOPES
                : SELF_MANAGED_INSTANCE_SEARCH_SCOPES;
    }
    const searchScope = await getSearchScope(possibleSearchScopes);
    if (!searchScope)
        return;
    const projectRestId = searchLevel === 'project' ? project.restId : null;
    const queryString = (0, create_query_string_1.createQueryString)({
        search: query,
        project_id: projectRestId,
        scope: searchScope,
    });
    // Select issues tab by default for Advanced Search
    await openers.openUrl(`${instanceUrl}/search${queryString}`);
};
exports.showAdvancedSearchInput = showAdvancedSearchInput;
//# sourceMappingURL=search_input.js.map