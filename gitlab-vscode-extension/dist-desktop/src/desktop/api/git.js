"use strict";
/* eslint-disable import/newline-after-import */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.GitErrorCodes = exports.Status = exports.RefType = exports.ForcePushMode = void 0;
var ForcePushMode;
(function (ForcePushMode) {
    ForcePushMode[ForcePushMode["Force"] = 0] = "Force";
    ForcePushMode[ForcePushMode["ForceWithLease"] = 1] = "ForceWithLease";
    ForcePushMode[ForcePushMode["ForceWithLeaseIfIncludes"] = 2] = "ForceWithLeaseIfIncludes";
})(ForcePushMode || (exports.ForcePushMode = ForcePushMode = {}));
var RefType;
(function (RefType) {
    RefType[RefType["Head"] = 0] = "Head";
    RefType[RefType["RemoteHead"] = 1] = "RemoteHead";
    RefType[RefType["Tag"] = 2] = "Tag";
})(RefType || (exports.RefType = RefType = {}));
var Status;
(function (Status) {
    Status[Status["INDEX_MODIFIED"] = 0] = "INDEX_MODIFIED";
    Status[Status["INDEX_ADDED"] = 1] = "INDEX_ADDED";
    Status[Status["INDEX_DELETED"] = 2] = "INDEX_DELETED";
    Status[Status["INDEX_RENAMED"] = 3] = "INDEX_RENAMED";
    Status[Status["INDEX_COPIED"] = 4] = "INDEX_COPIED";
    Status[Status["MODIFIED"] = 5] = "MODIFIED";
    Status[Status["DELETED"] = 6] = "DELETED";
    Status[Status["UNTRACKED"] = 7] = "UNTRACKED";
    Status[Status["IGNORED"] = 8] = "IGNORED";
    Status[Status["INTENT_TO_ADD"] = 9] = "INTENT_TO_ADD";
    Status[Status["INTENT_TO_RENAME"] = 10] = "INTENT_TO_RENAME";
    Status[Status["TYPE_CHANGED"] = 11] = "TYPE_CHANGED";
    Status[Status["ADDED_BY_US"] = 12] = "ADDED_BY_US";
    Status[Status["ADDED_BY_THEM"] = 13] = "ADDED_BY_THEM";
    Status[Status["DELETED_BY_US"] = 14] = "DELETED_BY_US";
    Status[Status["DELETED_BY_THEM"] = 15] = "DELETED_BY_THEM";
    Status[Status["BOTH_ADDED"] = 16] = "BOTH_ADDED";
    Status[Status["BOTH_DELETED"] = 17] = "BOTH_DELETED";
    Status[Status["BOTH_MODIFIED"] = 18] = "BOTH_MODIFIED";
})(Status || (exports.Status = Status = {}));
var GitErrorCodes;
(function (GitErrorCodes) {
    GitErrorCodes["BadConfigFile"] = "BadConfigFile";
    GitErrorCodes["AuthenticationFailed"] = "AuthenticationFailed";
    GitErrorCodes["NoUserNameConfigured"] = "NoUserNameConfigured";
    GitErrorCodes["NoUserEmailConfigured"] = "NoUserEmailConfigured";
    GitErrorCodes["NoRemoteRepositorySpecified"] = "NoRemoteRepositorySpecified";
    GitErrorCodes["NotAGitRepository"] = "NotAGitRepository";
    GitErrorCodes["NotAtRepositoryRoot"] = "NotAtRepositoryRoot";
    GitErrorCodes["Conflict"] = "Conflict";
    GitErrorCodes["StashConflict"] = "StashConflict";
    GitErrorCodes["UnmergedChanges"] = "UnmergedChanges";
    GitErrorCodes["PushRejected"] = "PushRejected";
    GitErrorCodes["ForcePushWithLeaseRejected"] = "ForcePushWithLeaseRejected";
    GitErrorCodes["ForcePushWithLeaseIfIncludesRejected"] = "ForcePushWithLeaseIfIncludesRejected";
    GitErrorCodes["RemoteConnectionError"] = "RemoteConnectionError";
    GitErrorCodes["DirtyWorkTree"] = "DirtyWorkTree";
    GitErrorCodes["CantOpenResource"] = "CantOpenResource";
    GitErrorCodes["GitNotFound"] = "GitNotFound";
    GitErrorCodes["CantCreatePipe"] = "CantCreatePipe";
    GitErrorCodes["PermissionDenied"] = "PermissionDenied";
    GitErrorCodes["CantAccessRemote"] = "CantAccessRemote";
    GitErrorCodes["RepositoryNotFound"] = "RepositoryNotFound";
    GitErrorCodes["RepositoryIsLocked"] = "RepositoryIsLocked";
    GitErrorCodes["BranchNotFullyMerged"] = "BranchNotFullyMerged";
    GitErrorCodes["NoRemoteReference"] = "NoRemoteReference";
    GitErrorCodes["InvalidBranchName"] = "InvalidBranchName";
    GitErrorCodes["BranchAlreadyExists"] = "BranchAlreadyExists";
    GitErrorCodes["NoLocalChanges"] = "NoLocalChanges";
    GitErrorCodes["NoStashFound"] = "NoStashFound";
    GitErrorCodes["LocalChangesOverwritten"] = "LocalChangesOverwritten";
    GitErrorCodes["NoUpstreamBranch"] = "NoUpstreamBranch";
    GitErrorCodes["IsInSubmodule"] = "IsInSubmodule";
    GitErrorCodes["WrongCase"] = "WrongCase";
    GitErrorCodes["CantLockRef"] = "CantLockRef";
    GitErrorCodes["CantRebaseMultipleBranches"] = "CantRebaseMultipleBranches";
    GitErrorCodes["PatchDoesNotApply"] = "PatchDoesNotApply";
    GitErrorCodes["NoPathFound"] = "NoPathFound";
    GitErrorCodes["UnknownPath"] = "UnknownPath";
    GitErrorCodes["EmptyCommitMessage"] = "EmptyCommitMessage";
    GitErrorCodes["BranchFastForwardRejected"] = "BranchFastForwardRejected";
    GitErrorCodes["BranchNotYetBorn"] = "BranchNotYetBorn";
    GitErrorCodes["TagConflict"] = "TagConflict";
    GitErrorCodes["CherryPickEmpty"] = "CherryPickEmpty";
    GitErrorCodes["CherryPickConflict"] = "CherryPickConflict";
})(GitErrorCodes || (exports.GitErrorCodes = GitErrorCodes = {}));
//# sourceMappingURL=git.js.map