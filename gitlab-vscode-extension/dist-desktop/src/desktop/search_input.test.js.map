{"version": 3, "file": "search_input.test.js", "sourceRoot": "", "sources": ["../../../src/desktop/search_input.test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,oDAA4B;AAC5B,2CAAuC;AACvC,kFAA6E;AAC7E,iDAMwB;AACxB,oDAA4D;AAC5D,4DAA8C;AAQ9C,MAAM,eAAe,GAAG,CAAC,MAAc,EAAE,EAAE,CACzC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACxC,MAAM;SACH,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SAC9B,KAAK,CAAC,GAAG,CAAC;SACV,IAAI,EAAE;SACN,IAAI,CAAC,GAAG,CAAC,CAAC;AAEf,SAAS,iBAAiB,CAExB,MAAc,EACd,QAAgB;IAEhB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC7E,IAAI,IAAI,EAAE,CAAC;QACT,OAAO;YACL,OAAO,EAAE,GAAG,EAAE,CACZ,YAAY,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,iBAAiB,IAAI,CAAC,KAAK,CAAC,aAAa,CACnF,QAAQ,CACT,EAAE;YACL,IAAI,EAAE,IAAI;SACX,CAAC;IACJ,CAAC;IACD,OAAO;QACL,OAAO,EAAE,GAAG,EAAE,CACZ,YAAY,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,aAAa,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;QAC/F,IAAI,EAAE,KAAK;KACZ,CAAC;AACJ,CAAC;AAED,gBAAM,CAAC,MAAM,CAAC;IACZ,iBAAiB;CAClB,CAAC,CAAC;AAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;IAC5B,MAAM,KAAK,GAAG,mBAAmB,CAAC;IAClC,MAAM,MAAM,GAAG,KAAK,CAAC;IACrB,MAAM,SAAS,GAAG,KAAK,CAAC;IACxB,MAAM,KAAK,GAAG,YAAY,CAAC;IAC3B,MAAM,MAAM,GAAG,uBAAuB,CAAC;IACvC,MAAM,WAAW,GAAG,CAAC,YAAY,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;IAC9D,MAAM,MAAM,GAAG,eAAe,CAAC;IAC/B,MAAM,QAAQ,GAAG,MAAM,CAAC;IACxB,MAAM,UAAU,GAAG,IAAI,CAAC;IACxB,MAAM,YAAY,GAAG,UAAU,CAAC;IAChC,MAAM,KAAK,GAAG,eAAe,CAAC;IAC9B,MAAM,eAAe,GAAG,KAAK,CAAC;IAC9B,MAAM,iBAAiB,GAAG,gBAAgB,CAAC;IAE3C,MAAM,+BAA+B,GAAG;QACtC,IAAI,EAAE,OAAO;QACb,MAAM,EAAE,QAAQ;QAChB,gBAAgB,EAAE,gBAAgB;QAClC,IAAI,EAAE,YAAY;QAClB,OAAO,EAAE,SAAS;QAClB,QAAQ,EAAE,OAAO;QACjB,UAAU,EAAE,YAAY;QACxB,KAAK,EAAE,OAAO;QACd,QAAQ,EAAE,UAAU;KACrB,CAAC;IAEF,MAAM,qBAAqB,GAAG;QAC5B,MAAM,EAAE,QAAQ;QAChB,gBAAgB,EAAE,gBAAgB;QAClC,QAAQ,EAAE,OAAO;QACjB,UAAU,EAAE,YAAY;QACxB,KAAK,EAAE,OAAO;QACd,QAAQ,EAAE,UAAU;KACrB,CAAC;IAEF,MAAM,mBAAmB,GAAG;QAC1B,IAAI,EAAE,OAAO;QACb,MAAM,EAAE,QAAQ;QAChB,gBAAgB,EAAE,gBAAgB;QAClC,IAAI,EAAE,YAAY;QAClB,OAAO,EAAE,SAAS;QAClB,QAAQ,EAAE,OAAO;QACjB,UAAU,EAAE,YAAY;QACxB,KAAK,EAAE,OAAO;KACf,CAAC;IAEF,MAAM,kBAAkB,GAAG;QACzB,KAAK,EAAE,SAAS;QAChB,WAAW,EAAE,SAAS;QACtB,WAAW,EAAE,yDAAyD;KACvE,CAAC;IAEF,MAAM,mBAAmB,GAAG;QAC1B,KAAK,EAAE,UAAU;QACjB,WAAW,EAAE,UAAU;QACvB,WAAW,EAAE,6CAA6C;KAC3D,CAAC;IAEF,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IAClD,MAAM,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IAC3D,MAAM,mBAAmB,GAAG,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;IAC/D,MAAM,6BAA6B,GAAG,MAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;IAEnF,MAAM,sBAAsB,GAAG,CAAC,WAAmB,EAAE,EAAE;QACrD,IAAI,CAAC,MAAM,CAAC,gBAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE,CAAC,WAAW,CAAC,CAAC;IACtF,CAAC,CAAC;IAEF,MAAM,2BAA2B,GAAG,CAAC,WAAwB,EAAE,WAAmB,EAAE,EAAE,CACpF,IAAI;SACD,MAAM,CAAC,gBAAM,CAAC,MAAM,CAAC,aAAa,CAAC;SACnC,qBAAqB,CACpB,IAAA,uCAAiB,EAAkB;QACjC,WAAW;KACZ,CAAC,CACH;QACD,8DAA8D;SAC7D,qBAAqB,CAAC,WAAkB,CAAC,CAAC,CAAC,qHAAqH;IAErK,UAAU,CAAC,GAAG,EAAE;QACd,IAAI,CAAC,MAAM,CAAC,gBAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE,CAAC,mBAAmB,CAAC,CAAC;IAC9F,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,MAAM,WAAW,GAAG,SAAS,CAAC;YAC9B,MAAM,WAAW,GAAG,MAAM,CAAC;YAC3B,MAAM,WAAW,GACf,GAAG,8BAAmB,CAAC,OAAO,CAAC,WAAW,GAAG;gBAC7C,iBAAiB,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBAC9C,eAAe,8BAAmB,CAAC,OAAO,CAAC,MAAM,EAAE;gBACnD,cAAc,CAAC;YACjB,2BAA2B,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YACtD,MAAM,IAAA,sCAAuB,EAAC,8BAAmB,CAAC,CAAC;YACnD,IAAA,gBAAM,EAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,2DAA2D,EAAE,KAAK,IAAI,EAAE;YACzE,MAAM,WAAW,GAAG,UAAU,CAAC;YAC/B,MAAM,WAAW,GAAG,QAAQ,CAAC;YAC7B,MAAM,WAAW,GACf,GAAG,8BAAmB,CAAC,OAAO,CAAC,WAAW,GAAG;gBAC7C,iBAAiB,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBAC9C,eAAe,CAAC;YAElB,2BAA2B,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YACtD,MAAM,IAAA,sCAAuB,EAAC,8BAAmB,CAAC,CAAC;YACnD,IAAA,gBAAM,EAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,+DAA+D,EAAE,KAAK,IAAI,EAAE;YAC7E,MAAM,WAAW,GAAG,UAAU,CAAC;YAC/B,MAAM,WAAW,GAAG,MAAM,CAAC;YAC3B,MAAM,WAAW,GAAG,2BAA2B,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YAC1E,MAAM,IAAA,sCAAuB,EAAC,8BAAmB,CAAC,CAAC;YAEnD,IAAA,gBAAM,EAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC;gBACrC;oBACE,CAAC,kBAAkB,EAAE,mBAAmB,CAAC;oBACzC;wBACE,KAAK,EAAE,mDAAmD;qBAC3D;iBACF;gBACD;oBACE,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC;oBAClC;wBACE,KAAK,EAAE,cAAc;qBACtB;iBACF;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,8DAA8D,EAAE,KAAK,IAAI,EAAE;YAC5E,MAAM,WAAW,GAAG,SAAS,CAAC;YAC9B,MAAM,WAAW,GAAG,OAAO,CAAC;YAC5B,MAAM,WAAW,GAAG,2BAA2B,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YAC1E,MAAM,IAAA,sCAAuB,EAAC,8BAAmB,CAAC,CAAC;YACnD,IAAA,gBAAM,EAAC,WAAW,CAAC,CAAC,cAAc,CAAC,iBAAiB,EAAE,gBAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,0EAA0E,EAAE,KAAK,IAAI,EAAE;YACxF,MAAM,WAAW,GAAG,UAAU,CAAC;YAC/B,MAAM,WAAW,GAAG,MAAM,CAAC;YAC3B,MAAM,WAAW,GAAG,2BAA2B,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YAC1E,MAAM,IAAA,sCAAuB,EAAC,8BAAmB,CAAC,CAAC;YACnD,IAAA,gBAAM,EAAC,WAAW,CAAC,CAAC,cAAc,CAAC,mBAAmB,EAAE,gBAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,4EAA4E,EAAE,KAAK,IAAI,EAAE;YAC1F,MAAM,WAAW,GAAG,UAAU,CAAC;YAC/B,MAAM,WAAW,GAAG,MAAM,CAAC;YAC3B,MAAM,WAAW,GAAG,2BAA2B,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YAC1E,8BAAmB,CAAC,OAAO,CAAC,WAAW,GAAG,gCAAgC,CAAC;YAC3E,MAAM,IAAA,sCAAuB,EAAC,8BAAmB,CAAC,CAAC;YACnD,IAAA,gBAAM,EAAC,WAAW,CAAC,CAAC,cAAc,CAAC,6BAA6B,EAAE,gBAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;YACzC,MAAM,iBAAiB,GACrB,GAAG,8BAAmB,CAAC,OAAO,CAAC,MAAM,EAAE;gBACvC,0BAA0B,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YAC1D,MAAM,IAAA,0CAA2B,EAAC,8BAAmB,CAAC,CAAC;YACvD,IAAA,gBAAM,EAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;YACxC,MAAM,iBAAiB,GACrB,GAAG,8BAAmB,CAAC,OAAO,CAAC,MAAM,EAAE;gBACvC,0BAA0B,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBACtD,WAAW,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBACpC,oBAAoB,SAAS,EAAE;gBAC/B,UAAU,KAAK,EAAE;gBACjB,sBAAsB,QAAQ,EAAE;gBAChC,oBAAoB,MAAM,EAAE,CAAC;YAC/B,sBAAsB,CACpB,UAAU,KAAK,GAAG;gBAChB,UAAU,KAAK,GAAG;gBAClB,WAAW,MAAM,GAAG;gBACpB,cAAc,SAAS,GAAG;gBAC1B,UAAU,KAAK,GAAG;gBAClB,aAAa,QAAQ,GAAG;gBACxB,WAAW,MAAM,GAAG,CACvB,CAAC;YACF,MAAM,IAAA,0CAA2B,EAAC,8BAAmB,CAAC,CAAC;YACvD,IAAA,gBAAM,EAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,gCAAgC,GAAG,GAAG,8BAAmB,CAAC,OAAO,CAAC,MAAM,yBAAyB,iBAAiB,EAAE,CAAC;YAC3H,sBAAsB,CAAC,aAAa,YAAY,EAAE,CAAC,CAAC;YACpD,MAAM,IAAA,0CAA2B,EAAC,8BAAmB,CAAC,CAAC;YACvD,IAAA,gBAAM,EAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,gCAAgC,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,gCAAgC,GAAG,GAAG,8BAAmB,CAAC,OAAO,CAAC,MAAM,yBAAyB,eAAe,EAAE,CAAC;YACzH,sBAAsB,CAAC,WAAW,UAAU,EAAE,CAAC,CAAC;YAChD,MAAM,IAAA,0CAA2B,EAAC,8BAAmB,CAAC,CAAC;YACvD,IAAA,gBAAM,EAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,gCAAgC,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;YACzC,MAAM,iBAAiB,GAAG,GAAG,8BAAmB,CAAC,OAAO,CAAC,MAAM,kBAAkB,MAAM;iBACpF,KAAK,CAAC,GAAG,CAAC;iBACV,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACf,MAAM,IAAA,mCAAoB,EAAC,8BAAmB,CAAC,CAAC;YAChD,IAAA,gBAAM,EAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;YACzC,MAAM,iBAAiB,GACrB,GAAG,8BAAmB,CAAC,OAAO,CAAC,MAAM,EAAE;gBACvC,kBAAkB,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBAC9C,WAAW,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBACpC,oBAAoB,SAAS,EAAE;gBAC/B,UAAU,KAAK,EAAE;gBACjB,4BAA4B,QAAQ,EAAE;gBACtC,oBAAoB,MAAM,EAAE,CAAC;YAC/B,sBAAsB,CACpB,UAAU,KAAK,GAAG;gBAChB,UAAU,KAAK,GAAG;gBAClB,WAAW,MAAM,GAAG;gBACpB,cAAc,SAAS,GAAG;gBAC1B,UAAU,KAAK,GAAG;gBAClB,aAAa,QAAQ,GAAG;gBACxB,WAAW,MAAM,GAAG,CACvB,CAAC;YACF,MAAM,IAAA,mCAAoB,EAAC,8BAAmB,CAAC,CAAC;YAChD,IAAA,gBAAM,EAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,MAAM,gCAAgC,GAAG,GAAG,8BAAmB,CAAC,OAAO,CAAC,MAAM,iBAAiB,iBAAiB,EAAE,CAAC;YACnH,sBAAsB,CAAC,aAAa,YAAY,EAAE,CAAC,CAAC;YACpD,MAAM,IAAA,mCAAoB,EAAC,8BAAmB,CAAC,CAAC;YAChD,IAAA,gBAAM,EAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,gCAAgC,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;YACzC,MAAM,gCAAgC,GAAG,GAAG,8BAAmB,CAAC,OAAO,CAAC,MAAM,iBAAiB,eAAe,EAAE,CAAC;YACjH,sBAAsB,CAAC,WAAW,UAAU,EAAE,CAAC,CAAC;YAChD,MAAM,IAAA,mCAAoB,EAAC,8BAAmB,CAAC,CAAC;YAChD,IAAA,gBAAM,EAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,gCAAgC,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}