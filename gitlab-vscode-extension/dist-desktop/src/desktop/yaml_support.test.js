"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const vscode_1 = __importDefault(require("vscode"));
const in_memory_memento_1 = require("../../test/integration/test_infrastructure/in_memory_memento");
const create_fake_partial_1 = require("../common/test_utils/create_fake_partial");
const yaml_support_1 = require("./yaml_support");
const constants_1 = require("./constants");
const confirmationMessageArguments = [
    "Would you like to install Red Hat's YAML extension to get real-time linting on the .gitlab-ci.yml file?",
    'Yes',
    'Not now',
    "Don't show again",
];
describe('yaml support', () => {
    let suggestionResponse;
    let fileName;
    let triggerOnDidOpenDocumentEvent;
    let context;
    let onDidOpenTextDocument;
    let showInformationMessage;
    const setup = async () => {
        (0, yaml_support_1.setupYamlSupport)(context);
        await triggerOnDidOpenDocumentEvent?.();
    };
    beforeEach(() => {
        fileName = '';
        context = (0, create_fake_partial_1.createFakePartial)({
            globalState: new in_memory_memento_1.InMemoryMemento(),
        });
        onDidOpenTextDocument = jest.fn(cb => {
            triggerOnDidOpenDocumentEvent = () => cb({ fileName });
        });
        showInformationMessage = jest.fn(() => Promise.resolve(suggestionResponse));
        vscode_1.default.window.showInformationMessage.mockImplementation(showInformationMessage);
        vscode_1.default.workspace.onDidOpenTextDocument.mockImplementation(onDidOpenTextDocument);
    });
    afterEach(() => {
        jest.resetAllMocks();
    });
    it('does nothing if extension is already installed', async () => {
        vscode_1.default.extensions.getExtension.mockImplementation(() => ({}));
        await setup();
        expect(onDidOpenTextDocument).not.toBeCalled();
    });
    it('does nothing if suggestion has been dismissed', async () => {
        await context.globalState.update(constants_1.DO_NOT_SHOW_YAML_SUGGESTION, true);
        await setup();
        await triggerOnDidOpenDocumentEvent();
        expect(showInformationMessage).not.toBeCalled();
    });
    describe('when file opened', () => {
        describe('when is yaml file', () => {
            beforeEach(() => {
                fileName = '.gitlab-ci.yml';
            });
            it('shows information message once per session', async () => {
                await setup();
                expect(showInformationMessage.mock.calls).toEqual([confirmationMessageArguments]);
                await triggerOnDidOpenDocumentEvent();
                expect(showInformationMessage.mock.calls).toEqual([confirmationMessageArguments]);
            });
            describe("when clicked 'Do not show again'", () => {
                beforeEach(async () => {
                    suggestionResponse = "Don't show again";
                    await setup();
                    await triggerOnDidOpenDocumentEvent();
                });
                it('shows information message once', () => {
                    expect(showInformationMessage.mock.calls).toEqual([confirmationMessageArguments]);
                });
                it('stores dismissal in globalState', () => {
                    expect(context.globalState.get(constants_1.DO_NOT_SHOW_YAML_SUGGESTION)).toBe(true);
                });
            });
        });
        describe('when is not yaml file', () => {
            beforeEach(() => {
                fileName = 'README.md';
            });
            it('does nothing', async () => {
                await setup();
                expect(showInformationMessage).not.toHaveBeenCalled();
            });
        });
    });
});
//# sourceMappingURL=yaml_support.test.js.map