"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const vscode_1 = __importDefault(require("vscode"));
const in_memory_memento_1 = require("../../test/integration/test_infrastructure/in_memory_memento");
const code_suggestions_promo_1 = require("./code_suggestions_promo");
const constants_1 = require("./constants");
const account_service_1 = require("./accounts/account_service");
jest.mock('./accounts/account_service', () => ({
    accountService: {
        onDidChange: jest.fn(() => ({
            dispose: jest.fn(),
        })),
        getAllAccounts: jest.fn(() => []),
    },
}));
const FAKE_GITLAB_ACCOUNT = {
    type: 'token',
    instanceUrl: 'https://gitlab.com',
    username: 'user',
    id: '1',
    token: '',
};
const FAKE_OTHER_INSTANCE_ACCOUNT = {
    ...FAKE_GITLAB_ACCOUNT,
    instanceUrl: 'https://some.other.host',
};
describe('Code suggestions promo', () => {
    let context;
    const disposeTextDocumentChange = jest.fn();
    beforeEach(() => {
        context = {
            globalState: new in_memory_memento_1.InMemoryMemento(),
        };
        jest
            .mocked(vscode_1.default.workspace.onDidChangeTextDocument)
            .mockReturnValue({ dispose: disposeTextDocumentChange });
    });
    it('does not subscribe to any events when promo was already dismissed', async () => {
        await context.globalState.update(constants_1.DISMISSED_CODE_SUGGESTIONS_PROMO, true);
        (0, code_suggestions_promo_1.setupCodeSuggestionsPromo)(context);
        expect(account_service_1.accountService.onDidChange).not.toHaveBeenCalled();
        expect(vscode_1.default.workspace.onDidChangeTextDocument).not.toHaveBeenCalled();
    });
    describe('when gitlab.com account is available', () => {
        beforeEach(() => {
            jest.mocked(account_service_1.accountService.getAllAccounts).mockReturnValueOnce([FAKE_GITLAB_ACCOUNT]);
            (0, code_suggestions_promo_1.setupCodeSuggestionsPromo)(context);
        });
        it('triggers promo on first text change', async () => {
            const [[listener]] = jest.mocked(vscode_1.default.workspace.onDidChangeTextDocument).mock.calls;
            listener({});
            expect(vscode_1.default.window.showInformationMessage).toHaveBeenCalled();
        });
        it('unsubscribes from text changes after first one', async () => {
            const [[listener]] = jest.mocked(vscode_1.default.workspace.onDidChangeTextDocument).mock.calls;
            jest.mocked(disposeTextDocumentChange).mockClear();
            await listener({});
            expect(disposeTextDocumentChange).toHaveBeenCalled();
        });
    });
    describe('when gitlab.com account is not available', () => {
        beforeEach(() => {
            (0, code_suggestions_promo_1.setupCodeSuggestionsPromo)(context);
        });
        it('triggers promo when gitlab.com account becomes available', () => {
            const [[listener]] = jest.mocked(account_service_1.accountService.onDidChange).mock.calls;
            jest.mocked(account_service_1.accountService.getAllAccounts).mockReturnValueOnce([FAKE_GITLAB_ACCOUNT]);
            listener();
            expect(vscode_1.default.window.showInformationMessage).toHaveBeenCalled();
        });
        it('does not trigger promo when non-gitlab.com account becomes available', () => {
            const [[listener]] = jest.mocked(account_service_1.accountService.onDidChange).mock.calls;
            jest.mocked(account_service_1.accountService.getAllAccounts).mockReturnValueOnce([FAKE_OTHER_INSTANCE_ACCOUNT]);
            listener();
            expect(vscode_1.default.window.showInformationMessage).not.toHaveBeenCalled();
        });
    });
});
//# sourceMappingURL=code_suggestions_promo.test.js.map