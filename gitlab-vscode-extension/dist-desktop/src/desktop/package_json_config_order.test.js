"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const packageJson = __importStar(require("../../package.json"));
const desktopPackageJson = __importStar(require("../../desktop.package.json"));
const getConfigPropertiesForCategory = (categoryId) => {
    const categoryConfig = {
        ...packageJson.contributes.configuration.find(c => c.id === categoryId)?.properties,
        ...desktopPackageJson.contributes.configuration.find(c => c.id === categoryId)?.properties,
    };
    return Object.entries(categoryConfig).map(([name, config]) => ({
        name,
        order: config.order,
    }));
};
const sortProperties = (properties) => {
    return [...properties]
        .sort((a, b) => (a.order ?? Number.POSITIVE_INFINITY) - (b.order ?? Number.POSITIVE_INFINITY))
        .map(p => p.name);
};
describe('config properties', () => {
    it('match defined order in duo category', () => {
        const expectedFirstProperties = [
            'gitlab.duoCodeSuggestions.enabled',
            'gitlab.duoChat.enabled',
            'gitlab.duoAgentPlatform.enabled',
            'gitlab.duoCodeSuggestions.enabledSupportedLanguages',
            'gitlab.duoAgentPlatform.connectionType',
            'gitlab.duoAgentPlatform.defaultNamespace',
            'gitlab.duoCodeSuggestions.additionalLanguages',
            'gitlab.duo.enabledWithoutGitlabProject',
            'gitlab.keybindingHints.enabled',
            'gitlab.duoCodeSuggestions.openTabsContext',
        ];
        const properties = getConfigPropertiesForCategory('duo');
        const sortedProperties = sortProperties(properties);
        const actualFirstProperties = sortedProperties.slice(0, expectedFirstProperties.length);
        expect(actualFirstProperties).toEqual(expectedFirstProperties);
    });
    it('match defined order in custom-certificates category', () => {
        const expectedFirstProperties = ['gitlab.ca', 'gitlab.cert', 'gitlab.certKey'];
        const properties = getConfigPropertiesForCategory('custom-certificates');
        const sortedProperties = sortProperties(properties);
        const actualFirstProperties = sortedProperties.slice(0, expectedFirstProperties.length);
        expect(actualFirstProperties).toEqual(expectedFirstProperties);
    });
    it('match defined order in other category', () => {
        const expectedFirstProperties = ['gitlab.debug'];
        const properties = getConfigPropertiesForCategory('other');
        const sortedProperties = sortProperties(properties);
        const actualFirstProperties = sortedProperties.slice(0, expectedFirstProperties.length);
        expect(actualFirstProperties).toEqual(expectedFirstProperties);
    });
});
//# sourceMappingURL=package_json_config_order.test.js.map