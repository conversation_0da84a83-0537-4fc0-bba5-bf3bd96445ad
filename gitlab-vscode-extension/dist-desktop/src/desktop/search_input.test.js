"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const vscode_1 = __importDefault(require("vscode"));
const globals_1 = require("@jest/globals");
const create_fake_partial_1 = require("../common/test_utils/create_fake_partial");
const search_input_1 = require("./search_input");
const entities_1 = require("./test_utils/entities");
const openers = __importStar(require("./commands/openers"));
const sortSearchQuery = (search) => search.slice(0, search.indexOf('?') + 1) +
    search
        .slice(search.indexOf('?') + 1)
        .split('&')
        .sort()
        .join('&');
function toBeEquivalentUrl(actual, expected) {
    const pass = this.equals(sortSearchQuery(actual), sortSearchQuery(expected));
    if (pass) {
        return {
            message: () => `expected ${this.utils.printReceived(actual)} not to match ${this.utils.printExpected(expected)}`,
            pass: true,
        };
    }
    return {
        message: () => `expected ${this.utils.printReceived(actual)} to match ${this.utils.printExpected(expected)}`,
        pass: false,
    };
}
globals_1.expect.extend({
    toBeEquivalentUrl,
});
describe('search input', () => {
    const title = 'my awesome search';
    const search = title;
    const milestone = '9.5';
    const label = 'discussion';
    const labels = 'frontend, performance';
    const labelsValue = ['discussion', 'frontend', 'performance'];
    const author = 'developerFace';
    const assignee = author;
    const selfAuthor = 'me';
    const selfAssignee = selfAuthor;
    const scope = 'created-by-me';
    const selfAuthorValue = scope;
    const selfAssigneeValue = 'assigned-to-me';
    const selfManagedInstanceSearchScopes = {
        Code: 'blobs',
        Issues: 'issues',
        'Merge Requests': 'merge_requests',
        Wiki: 'wiki_blobs',
        Commits: 'commits',
        Comments: 'notes',
        Milestones: 'milestones',
        Users: 'users',
        Projects: 'projects',
    };
    const gitlabComSearchScopes = {
        Issues: 'issues',
        'Merge Requests': 'merge_requests',
        Comments: 'notes',
        Milestones: 'milestones',
        Users: 'users',
        Projects: 'projects',
    };
    const projectSearchScopes = {
        Code: 'blobs',
        Issues: 'issues',
        'Merge Requests': 'merge_requests',
        Wiki: 'wiki_blobs',
        Commits: 'commits',
        Comments: 'notes',
        Milestones: 'milestones',
        Users: 'users',
    };
    const projectSearchLevel = {
        label: 'Project',
        searchLevel: 'project',
        description: 'The search includes only the current project in results',
    };
    const instanceSearchLevel = {
        label: 'Instance',
        searchLevel: 'instance',
        description: 'The search includes all projects in results',
    };
    const openUrlSpy = jest.spyOn(openers, 'openUrl');
    const projectSearchKeys = Object.keys(projectSearchScopes);
    const gitlabComSearchKeys = Object.keys(gitlabComSearchScopes);
    const selfManagedInstanceSearchKeys = Object.keys(selfManagedInstanceSearchScopes);
    const mockGetUserSearchInput = (searchInput) => {
        jest.mocked(vscode_1.default.window.showInputBox).mockImplementation(async () => searchInput);
    };
    const mockAdvanceSearchQuickPicks = (searchLevel, searchScope) => jest
        .mocked(vscode_1.default.window.showQuickPick)
        .mockResolvedValueOnce((0, create_fake_partial_1.createFakePartial)({
        searchLevel,
    }))
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .mockResolvedValueOnce(searchScope); // showQuickPick has a bunch of overloads, somehow Thenable<string> return type is not picked up here as a valid one.
    beforeEach(() => {
        jest.mocked(vscode_1.default.window.showInputBox).mockImplementation(async () => 'my awesome search');
    });
    describe('showAdvancedSearchInput', () => {
        it('contains a project id when search at the project level', async () => {
            const searchLevel = 'project';
            const searchScope = 'Code';
            const expectedUrl = `${entities_1.projectInRepository.account.instanceUrl}/` +
                `search?search=${search.split(' ').join('+')}` +
                `&project_id=${entities_1.projectInRepository.project.restId}` +
                `&scope=blobs`;
            mockAdvanceSearchQuickPicks(searchLevel, searchScope);
            await (0, search_input_1.showAdvancedSearchInput)(entities_1.projectInRepository);
            (0, globals_1.expect)(openUrlSpy.mock.calls[0][0]).toBeEquivalentUrl(expectedUrl);
        });
        it('does not set project id when search at the instance level', async () => {
            const searchLevel = 'instance';
            const searchScope = 'Issues';
            const expectedUrl = `${entities_1.projectInRepository.account.instanceUrl}/` +
                `search?search=${search.split(' ').join('+')}` +
                `&scope=issues`;
            mockAdvanceSearchQuickPicks(searchLevel, searchScope);
            await (0, search_input_1.showAdvancedSearchInput)(entities_1.projectInRepository);
            (0, globals_1.expect)(openUrlSpy.mock.calls[0][0]).toBeEquivalentUrl(expectedUrl);
        });
        it('presents the proper search scopes for instance level searches', async () => {
            const searchLevel = 'instance';
            const searchScope = 'Code';
            const mockedPicks = mockAdvanceSearchQuickPicks(searchLevel, searchScope);
            await (0, search_input_1.showAdvancedSearchInput)(entities_1.projectInRepository);
            (0, globals_1.expect)(mockedPicks.mock.calls).toEqual([
                [
                    [projectSearchLevel, instanceSearchLevel],
                    {
                        title: 'Search this project or the whole GitLab instance?',
                    },
                ],
                [
                    Object.keys(gitlabComSearchScopes),
                    {
                        title: 'Search scope',
                    },
                ],
            ]);
        });
        it('presents the proper search scopes for project level searches', async () => {
            const searchLevel = 'project';
            const searchScope = 'Users';
            const mockedPicks = mockAdvanceSearchQuickPicks(searchLevel, searchScope);
            await (0, search_input_1.showAdvancedSearchInput)(entities_1.projectInRepository);
            (0, globals_1.expect)(mockedPicks).lastCalledWith(projectSearchKeys, globals_1.expect.any(Object));
        });
        it('presents the proper search scopes for gitlab.com instance level searches', async () => {
            const searchLevel = 'instance';
            const searchScope = 'Code';
            const mockedPicks = mockAdvanceSearchQuickPicks(searchLevel, searchScope);
            await (0, search_input_1.showAdvancedSearchInput)(entities_1.projectInRepository);
            (0, globals_1.expect)(mockedPicks).lastCalledWith(gitlabComSearchKeys, globals_1.expect.any(Object));
        });
        it('presents the proper search scopes for self managed instance level searches', async () => {
            const searchLevel = 'instance';
            const searchScope = 'Code';
            const mockedPicks = mockAdvanceSearchQuickPicks(searchLevel, searchScope);
            entities_1.projectInRepository.account.instanceUrl = 'https://someprivate.gitlab.com';
            await (0, search_input_1.showAdvancedSearchInput)(entities_1.projectInRepository);
            (0, globals_1.expect)(mockedPicks).lastCalledWith(selfManagedInstanceSearchKeys, globals_1.expect.any(Object));
        });
    });
    describe('showMergeRequestSearchInput', () => {
        it('opens a basic text search', async () => {
            const expectedSearchUrl = `${entities_1.projectInRepository.project.webUrl}` +
                `/merge_requests?search=${search.split(' ').join('+')}`;
            await (0, search_input_1.showMergeRequestSearchInput)(entities_1.projectInRepository);
            (0, globals_1.expect)(openUrlSpy.mock.calls[0][0]).toBeEquivalentUrl(expectedSearchUrl);
        });
        it('supports filtered search', async () => {
            const expectedSearchUrl = `${entities_1.projectInRepository.project.webUrl}` +
                `/merge_requests?search=${title.split(' ').join('+')}` +
                `&labels=${labelsValue.join('%2C')}` +
                `&milestone_title=${milestone}` +
                `&scope=${scope}` +
                `&assignee_username=${assignee}` +
                `&author_username=${author}`;
            mockGetUserSearchInput(`title: ${title} ` +
                `label: ${label} ` +
                `labels: ${labels} ` +
                `milestone: ${milestone} ` +
                `scope: ${scope} ` +
                `assignee: ${assignee} ` +
                `author: ${author} `);
            await (0, search_input_1.showMergeRequestSearchInput)(entities_1.projectInRepository);
            (0, globals_1.expect)(openUrlSpy.mock.calls[0][0]).toBeEquivalentUrl(expectedSearchUrl);
        });
        it('finds merge requests assigned to me', async () => {
            const expectedSelfAssignedSearchResult = `${entities_1.projectInRepository.project.webUrl}/merge_requests?scope=${selfAssigneeValue}`;
            mockGetUserSearchInput(`assignee: ${selfAssignee}`);
            await (0, search_input_1.showMergeRequestSearchInput)(entities_1.projectInRepository);
            (0, globals_1.expect)(openUrlSpy.mock.calls[0][0]).toBeEquivalentUrl(expectedSelfAssignedSearchResult);
        });
        it('finds self created merge requests', async () => {
            const expectedSelfAssignedSearchResult = `${entities_1.projectInRepository.project.webUrl}/merge_requests?scope=${selfAuthorValue}`;
            mockGetUserSearchInput(`author: ${selfAuthor}`);
            await (0, search_input_1.showMergeRequestSearchInput)(entities_1.projectInRepository);
            (0, globals_1.expect)(openUrlSpy.mock.calls[0][0]).toBeEquivalentUrl(expectedSelfAssignedSearchResult);
        });
    });
    describe('showIssueSearchInput', () => {
        it('opens a basic text search', async () => {
            const expectedSearchUrl = `${entities_1.projectInRepository.project.webUrl}/issues?search=${search
                .split(' ')
                .join('+')}`;
            await (0, search_input_1.showIssueSearchInput)(entities_1.projectInRepository);
            (0, globals_1.expect)(openUrlSpy.mock.calls[0][0]).toBeEquivalentUrl(expectedSearchUrl);
        });
        it('supports filter searching', async () => {
            const expectedSearchUrl = `${entities_1.projectInRepository.project.webUrl}` +
                `/issues?search=${title.split(' ').join('+')}` +
                `&labels=${labelsValue.join('%2C')}` +
                `&milestone_title=${milestone}` +
                `&scope=${scope}` +
                `&assignee_username%5B%5D=${assignee}` +
                `&author_username=${author}`;
            mockGetUserSearchInput(`title: ${title} ` +
                `label: ${label} ` +
                `labels: ${labels} ` +
                `milestone: ${milestone} ` +
                `scope: ${scope} ` +
                `assignee: ${assignee} ` +
                `author: ${author} `);
            await (0, search_input_1.showIssueSearchInput)(entities_1.projectInRepository);
            (0, globals_1.expect)(openUrlSpy.mock.calls[0][0]).toBeEquivalentUrl(expectedSearchUrl);
        });
        it('finds issues assigned to me', async () => {
            const expectedSelfAssignedSearchResult = `${entities_1.projectInRepository.project.webUrl}/issues?scope=${selfAssigneeValue}`;
            mockGetUserSearchInput(`assignee: ${selfAssignee}`);
            await (0, search_input_1.showIssueSearchInput)(entities_1.projectInRepository);
            (0, globals_1.expect)(openUrlSpy.mock.calls[0][0]).toBeEquivalentUrl(expectedSelfAssignedSearchResult);
        });
        it('finds self created issues', async () => {
            const expectedSelfAssignedSearchResult = `${entities_1.projectInRepository.project.webUrl}/issues?scope=${selfAuthorValue}`;
            mockGetUserSearchInput(`author: ${selfAuthor}`);
            await (0, search_input_1.showIssueSearchInput)(entities_1.projectInRepository);
            (0, globals_1.expect)(openUrlSpy.mock.calls[0][0]).toBeEquivalentUrl(expectedSelfAssignedSearchResult);
        });
    });
});
//# sourceMappingURL=search_input.test.js.map