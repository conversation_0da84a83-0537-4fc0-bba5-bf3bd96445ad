"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.jobLogCache = exports.JobLogCache = void 0;
const vscode = __importStar(require("vscode"));
const job_log_refresher_1 = require("./job_log_refresher");
class JobLogCache {
    #onDidChangeEmitter = new vscode.EventEmitter();
    onDidJobChange = this.#onDidChangeEmitter.event;
    #storage = {};
    touch(jobId) {
        if (this.#storage[jobId]) {
            this.#storage[jobId].lastOpened = new Date().getTime();
        }
    }
    get(jobId) {
        return this.#storage[jobId];
    }
    set(jobId, rawTrace) {
        const exists = Boolean(this.#storage[jobId]);
        this.#storage[jobId]?.refresher?.dispose();
        this.#storage[jobId] = {
            rawTrace,
            eTag: null,
            lastOpened: this.#storage[jobId]?.lastOpened ?? new Date().getTime(),
        };
        if (exists)
            this.#onDidChangeEmitter.fire(jobId);
    }
    setForRunning(repositoryRoot, jobId, rawTrace, eTag) {
        const exists = Boolean(this.#storage[jobId]);
        this.#storage[jobId] = {
            repositoryRoot,
            rawTrace,
            lastOpened: this.#storage[jobId]?.lastOpened ?? new Date().getTime(),
            eTag,
            refresher: this.#storage[jobId]?.refresher,
        };
        if (exists)
            this.#onDidChangeEmitter.fire(jobId);
    }
    startRefreshing(projectId, jobId) {
        const item = this.#storage[jobId];
        if (!item || item.eTag === null || item.refresher)
            return;
        item.refresher = new job_log_refresher_1.JobLogRefresher(this, projectId, jobId);
    }
    stopRefreshing(jobId) {
        this.#storage[jobId]?.refresher?.dispose();
        delete this.#storage[jobId]?.refresher;
    }
    addDecorations(jobId, sections, decorations, filtered) {
        this.#storage[jobId] = {
            ...this.#storage[jobId],
            sections,
            decorations,
            filtered,
        };
    }
    async delete(jobId) {
        if (!this.#storage[jobId])
            return;
        // When a document changes its language, VS Code emits a close and open event in succession.
        // Delay the removal of the cache entry, and abort if the document was accessed during the timeout.
        const { lastOpened } = this.#storage[jobId];
        await new Promise(accept => {
            setTimeout(accept, 2000);
        });
        if (this.#storage[jobId]?.lastOpened === lastOpened) {
            this.#storage[jobId]?.refresher?.dispose();
            // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
            delete this.#storage[jobId];
        }
    }
    clearAll() {
        Object.values(this.#storage).forEach(v => v.refresher?.dispose());
        this.#storage = {};
    }
}
exports.JobLogCache = JobLogCache;
exports.jobLogCache = new JobLogCache();
//# sourceMappingURL=job_log_cache.js.map