"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const job_log_cache_1 = require("./job_log_cache");
describe('JobLogCache', () => {
    beforeEach(() => {
        jest.useFakeTimers();
    });
    afterEach(() => {
        jest.useRealTimers();
        jest.resetAllMocks();
        job_log_cache_1.jobLogCache.clearAll();
    });
    it('adds new items', () => {
        expect(job_log_cache_1.jobLogCache.get(123)).toBeUndefined();
        job_log_cache_1.jobLogCache.set(123, 'raw');
        expect(job_log_cache_1.jobLogCache.get(123)?.rawTrace).toBe('raw');
    });
    it('adds decorations to existing items', () => {
        job_log_cache_1.jobLogCache.set(123, 'raw');
        expect(job_log_cache_1.jobLogCache.get(123)?.filtered).toBeUndefined();
        job_log_cache_1.jobLogCache.addDecorations(123, new Map(), new Map(), 'filtered');
        expect(job_log_cache_1.jobLogCache.get(123)?.filtered).toBe('filtered');
    });
    it('removes a single item', async () => {
        job_log_cache_1.jobLogCache.set(123, 'raw');
        expect(job_log_cache_1.jobLogCache.get(123)).toBeDefined();
        const promise = job_log_cache_1.jobLogCache.delete(123);
        jest.runAllTimers();
        await promise;
        expect(job_log_cache_1.jobLogCache.get(123)).toBeUndefined();
    });
    it('aborts a removal when an item is touched', async () => {
        jest.setSystemTime(1665582000);
        job_log_cache_1.jobLogCache.set(123, 'raw');
        expect(job_log_cache_1.jobLogCache.get(123)).toBeDefined();
        jest.setSystemTime();
        const promise = job_log_cache_1.jobLogCache.delete(123);
        job_log_cache_1.jobLogCache.touch(123);
        jest.runAllTimers();
        await promise;
        expect(job_log_cache_1.jobLogCache.get(123)).toBeDefined();
    });
    it('clears everything immediately', () => {
        job_log_cache_1.jobLogCache.set(123, 'raw');
        job_log_cache_1.jobLogCache.set(456, 'raw');
        expect(job_log_cache_1.jobLogCache.get(123)).toBeDefined();
        expect(job_log_cache_1.jobLogCache.get(456)).toBeDefined();
        job_log_cache_1.jobLogCache.clearAll();
        expect(job_log_cache_1.jobLogCache.get(123)).toBeUndefined();
        expect(job_log_cache_1.jobLogCache.get(456)).toBeUndefined();
    });
});
//# sourceMappingURL=job_log_cache.test.js.map