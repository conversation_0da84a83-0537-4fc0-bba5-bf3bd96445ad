{"version": 3, "file": "current_branch_refresher.test.js", "sourceRoot": "", "sources": ["../../../src/desktop/current_branch_refresher.test.ts"], "names": [], "mappings": ";;AAAA,kFAA6E;AAC7E,qGAGyD;AACzD,yEAA2F;AAC3F,oDAQ+B;AAC/B,oEAA+D;AAC/D,6EAAuE;AACvE,+DAAyD;AACzD,4FAAoF;AACpF,kGAA4F;AAG5F,IAAI,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;AACrD,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;AACzC,IAAI,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;AAC5C,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;AACrC,IAAI,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;AACrD,IAAI,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;AAEjE,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;IACtC,UAAU,CAAC,GAAG,EAAE;QACd,IAAI;aACD,KAAK,CAAC,+DAA6B,EAAE,kBAAkB,CAAC;aACxD,eAAe,CAAC,IAAA,uCAAiB,EAAyB,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IACtF,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,2EAA2E,EAAE,KAAK,IAAI,EAAE;YACzF,MAAM,KAAK,GAAG,MAAM,iDAAsB,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YACtE,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,KAAK,IAAI,EAAE;YAC5E,IAAI,CAAC,MAAM,CAAC,gDAAqB,CAAC,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAC/D,MAAM,KAAK,GAAG,MAAM,iDAAsB,CAAC,QAAQ,CAAC,8BAAmB,EAAE,KAAK,CAAC,CAAC;YAChF,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,qCAAgB,CAAC,CAAC,eAAe,CAC3C,IAAA,uCAAiB,EAAgB;gBAC/B,kBAAkB,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC,IAAA,uCAAiB,EAAsB,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;gBACtF,qBAAqB,EAAE,KAAK,IAAI,EAAE,CAAC,gBAAK;gBACxC,kBAAkB,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC,cAAG,CAAC;gBACrC,yBAAyB,EAAE,KAAK,IAAI,EAAE,CAAC,EAAE;gBACzC,0BAA0B,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC,yBAAc,CAAC;gBACxD,YAAY,EAAE,KAAK,IAAO,EAAE,CAAC,CAAC,mBAAQ,CAAiB;aACxD,CAAC,CACH,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,0DAAyB,CAAC,CAAC,iBAAiB,CAAC,EAAE,QAAQ,EAAR,mBAAQ,EAAE,EAAE,EAAF,aAAE,EAAE,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,KAAK,IAAI,EAAE;YAC7E,IAAI,CAAC,MAAM,CAAC,gDAAqB,CAAC,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAC/D,MAAM,KAAK,GAAG,MAAM,iDAAsB,CAAC,QAAQ,CAAC,8BAAmB,EAAE,KAAK,CAAC,CAAC;YAEhF,MAAM,WAAW,GAAG,KAAoB,CAAC;YACzC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,mBAAQ,CAAC,CAAC;YAC/C,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,aAAE,CAAC,CAAC;YACnC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,gBAAK,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kFAAkF,EAAE,KAAK,IAAI,EAAE;YAChG,IAAI,CAAC,MAAM,CAAC,gDAAqB,CAAC,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAC/D,IAAI,CAAC,MAAM,CAAC,gDAAqB,CAAC,CAAC,iBAAiB,CAAC,iCAAsB,CAAC,CAAC;YAE7E,MAAM,KAAK,GAAG,MAAM,iDAAsB,CAAC,QAAQ,CAAC,8BAAmB,EAAE,KAAK,CAAC,CAAC;YAEhF,MAAM,WAAW,GAAG,KAAoB,CAAC;YACzC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,mBAAQ,CAAC,CAAC;YAC/C,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,aAAE,CAAC,CAAC;YACnC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,gBAAK,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,iCAAsB,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;YACvE,IAAI,CAAC,MAAM,CAAC,gDAAqB,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAChE,IAAI,CAAC,MAAM,CAAC,kCAAc,CAAC,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACxD,MAAM,KAAK,GAAG,MAAM,iDAAsB,CAAC,QAAQ,CAAC,8BAAmB,EAAE,KAAK,CAAC,CAAC;YAEhF,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/B,MAAM,CAAE,KAAkB,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,mBAAQ,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,IAAI,CAAC,MAAM,CAAC,gDAAqB,CAAC,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAC/D,MAAM,KAAK,GAAG,MAAM,iDAAsB,CAAC,QAAQ,CAAC,8BAAmB,EAAE,KAAK,CAAC,CAAC;YAEhF,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClC,MAAM,CAAE,KAAqB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,cAAG,EAAE,yBAAc,CAAC,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}