"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.issuableController = void 0;
const path = __importStar(require("path"));
const assert_1 = __importDefault(require("assert"));
const vscode = __importStar(require("vscode"));
const log_1 = require("../common/log");
const handle_error_1 = require("../common/errors/handle_error");
const wait_for_webview_1 = require("../common/utils/webviews/wait_for_webview");
const prepare_webview_source_1 = require("../common/utils/webviews/prepare_webview_source");
const is_mr_1 = require("./utils/is_mr");
const make_html_links_absolute_1 = require("./utils/make_html_links_absolute");
const gitlab_project_repository_1 = require("./gitlab/gitlab_project_repository");
const get_gitlab_service_1 = require("./gitlab/get_gitlab_service");
const constants_1 = require("./constants");
async function initPanelIfActive(panel, issuable, gitlabService) {
    if (!panel.active)
        return;
    const waitPromise = (0, wait_for_webview_1.waitForWebview)(panel.webview);
    const discussionsAndLabels = await gitlabService
        .getDiscussionsAndLabelEvents(issuable)
        .catch(e => {
        (0, handle_error_1.handleError)(e);
        return [];
    });
    await waitPromise;
    await panel.webview.postMessage({
        type: 'issuableFetch',
        issuable,
        discussions: discussionsAndLabels,
    });
}
class IssuableController {
    context;
    openedPanels = {};
    init(context) {
        this.context = context;
    }
    #createPanel(issuable) {
        (0, assert_1.default)(this.context);
        const title = `${issuable.title.slice(0, 20)}...`;
        return vscode.window.createWebviewPanel(constants_1.WEBVIEW_WORKFLOW, title, vscode.ViewColumn.One, {
            enableScripts: true,
            localResourceRoots: [
                vscode.Uri.file(path.join(this.context.extensionPath, 'webviews')),
                vscode.Uri.file(path.join(this.context.extensionPath, 'assets')),
            ],
            retainContextWhenHidden: true,
        });
    }
    #createMessageHandler = (panel, issuable, projectInRepository) => 
    // FIXME: specify correct type
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    async (message) => {
        if (message.command === 'renderMarkdown') {
            let rendered = await (0, get_gitlab_service_1.getGitLabService)(projectInRepository).renderMarkdown(message.markdown, projectInRepository.project);
            rendered = (0, make_html_links_absolute_1.makeHtmlLinksAbsolute)(rendered || '', projectInRepository.account.instanceUrl);
            await panel.webview.postMessage({
                type: 'markdownRendered',
                ref: message.ref,
                object: message.object,
                markdown: rendered,
            });
        }
        if (message.command === 'saveNote') {
            try {
                const gitlabService = (0, get_gitlab_service_1.getGitLabService)(projectInRepository);
                try {
                    await gitlabService.createNote(issuable, message.note, message.replyId);
                }
                catch (error) {
                    // See https://gitlab.com/gitlab-org/gitlab-vscode-extension/-/issues/357
                    const isCommentOnlyError = String(error?.details?.errorMessage?.startsWith('Commands only'));
                    if (!isCommentOnlyError) {
                        throw error;
                    }
                }
                const discussionsAndLabels = await gitlabService.getDiscussionsAndLabelEvents(issuable);
                await panel.webview.postMessage({
                    type: 'issuableFetch',
                    issuable,
                    discussions: discussionsAndLabels,
                });
                await panel.webview.postMessage({ type: 'noteSaved' });
            }
            catch (e) {
                log_1.log.error('Failed to submit note to the API.', e);
                await panel.webview.postMessage({ type: 'noteSaved', status: false });
            }
        }
    };
    #getIconPathForIssuable(issuable) {
        const getIconUri = (shade, file) => vscode.Uri.file(path.join(this.context?.extensionPath || '', 'src', 'assets', 'images', shade, file));
        const lightIssueIcon = getIconUri('light', 'issues.svg');
        const lightMrIcon = getIconUri('light', 'merge_requests.svg');
        const darkIssueIcon = getIconUri('dark', 'issues.svg');
        const darkMrIcon = getIconUri('dark', 'merge_requests.svg');
        return (0, is_mr_1.isMr)(issuable)
            ? { light: lightMrIcon, dark: darkMrIcon }
            : { light: lightIssueIcon, dark: darkIssueIcon };
    }
    async open(issuable, repositoryRoot) {
        const panelKey = `${repositoryRoot}-${issuable.id}`;
        const openedPanel = this.openedPanels[panelKey];
        if (openedPanel) {
            openedPanel.reveal();
            return openedPanel;
        }
        const newPanel = await this.#create(issuable, repositoryRoot);
        this.openedPanels[panelKey] = newPanel;
        newPanel.onDidDispose(() => {
            this.openedPanels[panelKey] = undefined;
        });
        return newPanel;
    }
    async #create(issuable, repositoryRoot) {
        (0, assert_1.default)(this.context);
        const projectInRepository = (0, gitlab_project_repository_1.getProjectRepository)().getProjectOrFail(repositoryRoot);
        const panel = this.#createPanel(issuable);
        panel.webview.html = await (0, prepare_webview_source_1.prepareWebviewSource)(panel.webview, this.context, 'issuable');
        panel.iconPath = this.#getIconPathForIssuable(issuable);
        panel.repositoryRoot = repositoryRoot;
        await initPanelIfActive(panel, issuable, (0, get_gitlab_service_1.getGitLabService)(projectInRepository));
        panel.onDidChangeViewState(async () => {
            await initPanelIfActive(panel, issuable, (0, get_gitlab_service_1.getGitLabService)(projectInRepository));
        });
        panel.webview.onDidReceiveMessage(this.#createMessageHandler(panel, issuable, projectInRepository));
        return panel;
    }
    matchesViewType(viewType) {
        return viewType === `mainThreadWebview-${constants_1.WEBVIEW_WORKFLOW}`;
    }
    get repositoryRootForActiveTab() {
        const panel = Object.values(this.openedPanels).find(p => p?.active);
        return panel?.repositoryRoot;
    }
}
exports.issuableController = new IssuableController();
//# sourceMappingURL=issuable_controller.js.map