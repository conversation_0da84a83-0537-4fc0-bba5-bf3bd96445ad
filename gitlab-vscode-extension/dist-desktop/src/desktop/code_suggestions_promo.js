"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupCodeSuggestionsPromo = void 0;
const vscode = __importStar(require("vscode"));
const constants_1 = require("../common/constants");
const constants_2 = require("./constants");
const account_service_1 = require("./accounts/account_service");
const DOCS_LINK = 'https://docs.gitlab.com/user/project/repository/code_suggestions/';
function isImmediatelyEligibleForPromo() {
    return account_service_1.accountService.getAllAccounts().some(account => account.instanceUrl === constants_1.GITLAB_COM_URL);
}
// FIXME: Custom messages like these are deprecated in favour of the
// src/common/user_message.ts component
async function showPromo() {
    const LEARN_MORE_ACTION = 'Learn more';
    const selection = await vscode.window.showInformationMessage('Get started with Code Suggestions. Code faster and more efficiently with AI-powered code suggestions in VS Code. Many languages are supported, including JavaScript, Python, Go, Java, Kotlin and Terraform. Enable Code Suggestions in your user profile, or see the documentation to learn more.', LEARN_MORE_ACTION, 'Dismiss');
    if (selection === LEARN_MORE_ACTION) {
        await vscode.env.openExternal(vscode.Uri.parse(DOCS_LINK));
    }
    return selection;
}
function markBannerAsDismissed(context) {
    return context.globalState.update(constants_2.DISMISSED_CODE_SUGGESTIONS_PROMO, true);
}
const setupCodeSuggestionsPromo = (context) => {
    if (context.globalState.get(constants_2.DISMISSED_CODE_SUGGESTIONS_PROMO))
        return;
    if (isImmediatelyEligibleForPromo()) {
        const changeListener = vscode.workspace.onDidChangeTextDocument(async () => {
            await showPromo();
            await markBannerAsDismissed(context);
            changeListener.dispose();
        });
    }
    else {
        const accountChangeListener = account_service_1.accountService.onDidChange(async () => {
            if (isImmediatelyEligibleForPromo()) {
                await showPromo();
                await markBannerAsDismissed(context);
                accountChangeListener.dispose();
            }
        });
    }
};
exports.setupCodeSuggestionsPromo = setupCodeSuggestionsPromo;
//# sourceMappingURL=code_suggestions_promo.js.map