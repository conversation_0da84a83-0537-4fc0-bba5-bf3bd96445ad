"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.REQUIRED_VERSIONS = exports.FAILED_COMMENT_CONTEXT = exports.SYNCED_COMMENT_CONTEXT = exports.OAUTH_REDIRECT_URI = exports.OAUTH_CLIENT_ID = exports.PATCH_FILE_SUFFIX = exports.PATCH_TITLE_PREFIX = exports.HAS_COMMENTS_QUERY_KEY = exports.CHANGE_TYPE_QUERY_KEY = exports.WEBVIEW_SECURITY_FINDING = exports.WEBVIEW_PENDING_JOB = exports.WEBVIEW_WORKFLOW = exports.DISMISSED_CODE_SUGGESTIONS_PROMO = exports.DO_NOT_SHOW_YAML_SUGGESTION = exports.MODIFIED = exports.RENAMED = exports.DELETED = exports.ADDED = exports.REMOTE_URI_SCHEME = exports.JOB_LOG_URI_SCHEME = exports.MERGED_YAML_URI_SCHEME = exports.REVIEW_URI_SCHEME = void 0;
const vscode_1 = __importDefault(require("vscode"));
exports.REVIEW_URI_SCHEME = 'gl-review';
exports.MERGED_YAML_URI_SCHEME = 'gl-merged-ci-yaml';
exports.JOB_LOG_URI_SCHEME = 'gl-job-log';
exports.REMOTE_URI_SCHEME = 'gitlab-remote';
exports.ADDED = 'added';
exports.DELETED = 'deleted';
exports.RENAMED = 'renamed';
exports.MODIFIED = 'modified';
exports.DO_NOT_SHOW_YAML_SUGGESTION = 'DO_NOT_SHOW_YAML_SUGGESTION';
exports.DISMISSED_CODE_SUGGESTIONS_PROMO = 'DISMISSED_CODE_SUGGESTIONS_PROMO';
exports.WEBVIEW_WORKFLOW = 'glWorkflow';
exports.WEBVIEW_PENDING_JOB = 'gitlab.waitForPendingJob';
exports.WEBVIEW_SECURITY_FINDING = 'gitlab.viewSecurityFinding';
exports.CHANGE_TYPE_QUERY_KEY = 'changeType';
exports.HAS_COMMENTS_QUERY_KEY = 'hasComments';
exports.PATCH_TITLE_PREFIX = 'patch: ';
exports.PATCH_FILE_SUFFIX = '.patch';
exports.OAUTH_CLIENT_ID = '36f2a70cddeb5a0889d4fd8295c241b7e9848e89cf9e599d0eed2d8e5350fbf5';
exports.OAUTH_REDIRECT_URI = `${vscode_1.default.env.uriScheme}://gitlab.gitlab-workflow/authentication`;
/** Synced comment is stored in the GitLab instance */
exports.SYNCED_COMMENT_CONTEXT = 'synced-comment';
/** Failed comment is only stored in the extension, it failed to be created in GitLab */
exports.FAILED_COMMENT_CONTEXT = 'failed-comment';
exports.REQUIRED_VERSIONS = {
    // NOTE: This needs to _always_ be a 3 digits
    CI_CONFIG_VALIDATIONS: '13.6.0',
    MR_DISCUSSIONS: '13.9.0',
    MR_MERGE_QUICK_ACTION: '14.9.0', // https://gitlab.com/gitlab-org/gitlab-vscode-extension/-/issues/545
    SECURITY_FINDINGS: '16.1.0',
    BRANCH_PROTECTION: '16.9.0',
};
//# sourceMappingURL=constants.js.map