"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.gitlabUriHandler = exports.GitLabUriHandler = void 0;
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See LICENSE in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
/* Taken from https://github.com/microsoft/vscode/blob/3be2b5d9524ef9d6fda0c2751ec2d02a181ea6f4/extensions/github-authentication/src/githubServer.ts#L28-L37 */
const vscode_1 = __importDefault(require("vscode"));
class GitLabUriHandler extends vscode_1.default.EventEmitter {
    async handleUri(uri) {
        this.fire(uri);
    }
}
exports.GitLabUriHandler = GitLabUriHandler;
exports.gitlabUriHandler = new GitLabUriHandler();
//# sourceMappingURL=gitlab_uri_handler.js.map