{"version": 3, "file": "extension_state.test.js", "sourceRoot": "", "sources": ["../../../src/desktop/extension_state.test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,4DAAwD;AACxD,kFAA6E;AAC7E,uDAAmD;AACnD,uEAAkE;AAElE,oFAM8C;AAC9C,oDAA2D;AAE3D,IAAI,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;AAErD,MAAM,eAAe,GAA0B;IAC7C,IAAI,EAAE,uCAAW;CAClB,CAAC;AAEF,MAAM,kBAAkB,GAA0B;IAChD,IAAI,EAAE,0CAAc;IACpB,OAAO,EAAP,kBAAO;CACR,CAAC;AAEF,MAAM,qBAAqB,GAA0B;IACnD,IAAI,EAAE,uDAA2B;IACjC,iBAAiB,EAAE,CAAC,IAAA,6BAAkB,EAAC,GAAG,CAAC,EAAE,IAAA,6BAAkB,EAAC,GAAG,CAAC,CAAC;CACtE,CAAC;AAEF,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;IAC/B,IAAI,cAA8B,CAAC;IACnC,IAAI,WAAkC,CAAC;IACvC,IAAI,kBAAmC,CAAC;IACxC,IAAI,oBAA6C,CAAC;IAElD,UAAU,CAAC,GAAG,EAAE;QACd,kBAAkB,GAAG,EAAE,CAAC;QACxB,WAAW,GAAG,eAAe,CAAC;QAC9B,IAAI;aACD,KAAK,CAAC,2CAAmB,EAAE,iBAAiB,EAAE,KAAK,CAAC;aACpD,kBAAkB,CAAC,GAAG,EAAE,CAAC,kBAAkB,CAAC,CAAC;QAChD,oBAAoB,GAAG,IAAA,uCAAiB,EAA0B;YAChE,IAAI,KAAK;gBACP,OAAO,WAAW,CAAC;YACrB,CAAC;YACD,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;SACpB,CAAC,CAAC;QACH,cAAc,GAAG,IAAI,gCAAc,CAAC,oBAAoB,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,IAAI,CAAA;;MAEH,YAAY,2BAA2B,eAAe,SAAS,EAAE,oBAAoB,KAAK,QAAQ,IAAI,QAAQ,CAAC;MAC/G,6BAA6B,UAAU,eAAe,SAAS,CAAC,YAAY,CAAC,QAAQ,KAAK,QAAQ,IAAI,QAAQ,CAAC;MAC/G,iCAAiC,MAAM,kBAAkB,MAAM,EAAE,oBAAoB,KAAK,QAAQ,KAAK,OAAO,CAAC;MAC/G,UAAU,6BAA6B,kBAAkB,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,MAAM,IAAI,SAAS,KAAK,OAAO,CAAC;GAClH,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,mBAAmB,EAAE,EAAE,EAAE;QAC3F,WAAW,GAAG,KAAK,CAAC;QACpB,kBAAkB,GAAG,YAAY,CAAC;QAClC,MAAM,cAAc,CAAC,IAAI,EAAE,CAAC;QAE5B,MAAM,EAAE,cAAc,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC;QAC3C,MAAM,CAAC,cAAc,CAAC,CAAC,oBAAoB,CAAC,YAAY,EAAE,mBAAmB,EAAE,UAAU,CAAC,CAAC;QAC3F,MAAM,CAAC,cAAc,CAAC,CAAC,oBAAoB,CAAC,YAAY,EAAE,kBAAkB,EAAE,SAAS,CAAC,CAAC;QACzF,MAAM,CAAC,cAAc,CAAC,CAAC,oBAAoB,CACzC,YAAY,EACZ,4BAA4B,EAC5B,mBAAmB,CACpB,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,UAAU,CAAC,GAAG,EAAE;YACd,WAAW,GAAG,qBAAqB,CAAC;YACpC,kBAAkB,GAAG,CAAC,EAAE,UAAU,EAAE,YAAY,EAAmB,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,YAAY,EAAE,KAAK,IAAI,EAAE;YAC1B,MAAM,cAAc,CAAC,IAAI,EAAE,CAAC;YAE5B,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;QACpD,MAAM,cAAc,CAAC,IAAI,EAAE,CAAC;QAC5B,MAAM,oBAAoB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QACvC,cAAc,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,CAAC;QACtD,8DAA8D;QAC9D,WAAW,GAAG,kBAAkB,CAAC;QACjC,kBAAkB,GAAG,CAAC,EAAE,UAAU,EAAE,YAAY,EAAmB,CAAC,CAAC;QAErE,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7E,MAAM,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,kBAAkB,CAAC,CAAC;QAExD,MAAM,CAAC,oBAAoB,CAAC,CAAC,gBAAgB,EAAE,CAAC;IAClD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}