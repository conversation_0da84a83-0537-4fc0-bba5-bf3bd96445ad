"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.keepAliveHeader = void 0;
exports.updateUserProxyUrl = updateUserProxyUrl;
exports.initializeNetworkAgent = initializeNetworkAgent;
const https_1 = __importDefault(require("https"));
const http_1 = __importDefault(require("http"));
const vscode = __importStar(require("vscode"));
const log_1 = require("../common/log");
const get_http_agent_options_1 = require("./gitlab/http/get_http_agent_options");
const httpsAgent = new https_1.default.Agent((0, get_http_agent_options_1.getHttpAgentOptions)());
const httpAgent = new http_1.default.Agent((0, get_http_agent_options_1.getHttpAgentOptions)());
// The path to the exported class can be found in the npm contents
// https://www.npmjs.com/package/@vscode/proxy-agent?activeTab=code
const nodeModules = '_VSCODE_NODE_MODULES';
const proxyAgentPath = '@vscode/proxy-agent/out/agent';
const proxyAgent = 'PacProxyAgent';
exports.keepAliveHeader = 'keep-alive';
let userProxyUrl = '';
function updateUserProxyUrl() {
    userProxyUrl = vscode.workspace.getConfiguration('http').get('proxy') || '';
}
function initializeNetworkAgent() {
    const customAgent = ({ protocol }) => {
        if (protocol === 'http:') {
            return httpAgent;
        }
        return httpsAgent;
    };
    updateUserProxyUrl();
    /**
     * This works around an issue in the default VS Code proxy agent code. When `http.proxySupport`
     * is set to its default value and no proxy setting is being used, the proxy library does not
     * properly reuse the agent set on the http(s) method and is instead always using a new agent
     * per request.
     *
     * To work around this, we patch the default proxy agent method and overwrite the
     * `originalAgent` value before invoking it for requests that want to keep their connection
     * alive only when user is not using their own http proxy and the request contains keepAliveHeader
     *
     * c.f. https://github.com/microsoft/vscode/issues/173861
     * code reference: https://github.com/sourcegraph/cody/pull/868/files
     */
    try {
        const PacProxyAgent = 
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        globalThis?.[nodeModules]?.[proxyAgentPath]?.[proxyAgent] ?? undefined;
        if (PacProxyAgent) {
            const originalConnect = PacProxyAgent.prototype.connect;
            // Patches the implementation defined here:
            // https://github.com/microsoft/vscode-proxy-agent/blob/d340b9d34684da494d6ebde3bcd18490a8bbd071/src/agent.ts#L53
            PacProxyAgent.prototype.connect = function (req, opts) {
                try {
                    const connectionHeader = req.getHeader('connection');
                    const connectionHeaderHasKeepAlive = connectionHeader === exports.keepAliveHeader ||
                        (Array.isArray(connectionHeader) && connectionHeader.includes(exports.keepAliveHeader));
                    if (connectionHeaderHasKeepAlive && userProxyUrl === '') {
                        this.opts.originalAgent = customAgent(opts);
                        return originalConnect.call(this, req, opts);
                    }
                    return originalConnect.call(this, req, opts);
                }
                catch {
                    return originalConnect.call(this, req, opts);
                }
            };
        }
        else {
            log_1.log.info('PacProxyAgent not found');
        }
    }
    catch (error) {
        // Log any errors in the patching logic
        log_1.log.error('Failed to patch http agent', error);
    }
}
//# sourceMappingURL=fetch.js.map