"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createDependencyContainer = void 0;
const ls_git_provider_1 = require("./git/ls_git_provider");
const gitlab_platform_desktop_1 = require("./gitlab/gitlab_platform_desktop");
const gitlab_telemetry_environment_desktop_1 = require("./gitlab/gitlab_telemetry_environment_desktop");
const createDependencyContainer = () => ({
    gitLabPlatformManager: gitlab_platform_desktop_1.gitlabPlatformManagerDesktop,
    gitLabTelemetryEnvironment: new gitlab_telemetry_environment_desktop_1.GitLabTelemetryEnvironmentDesktop(),
    lsGitProvider: new ls_git_provider_1.LSGitProviderDesktop(),
});
exports.createDependencyContainer = createDependencyContainer;
//# sourceMappingURL=dependency_container_desktop.js.map