"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const vscode = __importStar(require("vscode"));
const entities_1 = require("../common/test_utils/entities");
const create_fake_partial_1 = require("../common/test_utils/create_fake_partial");
const extension_state_1 = require("./extension_state");
const git_extension_wrapper_1 = require("./git/git_extension_wrapper");
const workspace_account_manager_1 = require("./accounts/workspace_account_manager");
const entities_2 = require("./test_utils/entities");
jest.mock('../common/utils/extension_configuration');
const noAccountsState = {
    type: workspace_account_manager_1.NO_ACCOUNTS,
};
const singleAccountState = {
    type: workspace_account_manager_1.SINGLE_ACCOUNT,
    account: entities_1.account,
};
const multipleAccountsState = {
    type: workspace_account_manager_1.MULTIPLE_AVAILABLE_ACCOUNTS,
    availableAccounts: [(0, entities_2.createTokenAccount)('A'), (0, entities_2.createTokenAccount)('B')],
};
describe('extension_state', () => {
    let extensionState;
    let mockedState;
    let mockedRepositories;
    let mockedAccountManager;
    beforeEach(() => {
        mockedRepositories = [];
        mockedState = noAccountsState;
        jest
            .spyOn(git_extension_wrapper_1.gitExtensionWrapper, 'gitRepositories', 'get')
            .mockImplementation(() => mockedRepositories);
        mockedAccountManager = (0, create_fake_partial_1.createFakePartial)({
            get state() {
                return mockedState;
            },
            onChange: jest.fn(),
        });
        extensionState = new extension_state_1.ExtensionState(mockedAccountManager);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    it.each `
    scenario                             | state                 | repositories        | validState | noAccount | openRepositoryCount
    ${'is invalid'}                      | ${noAccountsState}    | ${[]}               | ${false}   | ${true}   | ${0}
    ${'is invalid without accounts'}     | ${noAccountsState}    | ${['repository']}   | ${false}   | ${true}   | ${1}
    ${'is invalid without repositories'} | ${singleAccountState} | ${[]}               | ${false}   | ${false}  | ${0}
    ${'is valid'}                        | ${singleAccountState} | ${[['repository']]} | ${true}    | ${false}  | ${1}
  `('$scenario', async ({ state, repositories, validState, noAccount, openRepositoryCount }) => {
        mockedState = state;
        mockedRepositories = repositories;
        await extensionState.init();
        const { executeCommand } = vscode.commands;
        expect(executeCommand).toHaveBeenCalledWith('setContext', 'gitlab:validState', validState);
        expect(executeCommand).toHaveBeenCalledWith('setContext', 'gitlab:noAccount', noAccount);
        expect(executeCommand).toHaveBeenCalledWith('setContext', 'gitlab:openRepositoryCount', openRepositoryCount);
    });
    describe('when we should select account', () => {
        beforeEach(() => {
            mockedState = multipleAccountsState;
            mockedRepositories = [{ rootFsPath: 'repository' }];
        });
        it('is invalid', async () => {
            await extensionState.init();
            expect(extensionState.isValid()).toBe(false);
        });
    });
    it('fires event when valid state changes', async () => {
        await extensionState.init();
        const stateChangedListener = jest.fn();
        extensionState.onDidChangeValid(stateChangedListener);
        // setting tokens and repositories makes extension state valid
        mockedState = singleAccountState;
        mockedRepositories = [{ rootFsPath: 'repository' }];
        const listener = jest.mocked(mockedAccountManager.onChange).mock.calls[0][0];
        await listener.bind(extensionState)(singleAccountState);
        expect(stateChangedListener).toHaveBeenCalled();
    });
});
//# sourceMappingURL=extension_state.test.js.map