"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = void 0;
const vscode_1 = __importDefault(require("vscode"));
const source_map_support_1 = require("source-map-support");
const log_1 = require("../common/log");
const handle_error_1 = require("../common/errors/handle_error");
const do_not_await_1 = require("../common/utils/do_not_await");
const extension_configuration_1 = require("../common/utils/extension_configuration");
const local_feature_flag_service_1 = require("../common/feature_flags/local_feature_flag_service");
const main_1 = require("../common/main");
const wrap_command_with_catch_1 = require("../common/utils/wrap_command_with_catch");
const language_server_manager_1 = require("../common/language_server/language_server_manager");
const code_suggestions_1 = require("../common/code_suggestions/code_suggestions");
const webview_1 = require("../common/webview");
const migrations_1 = require("../common/utils/extension_configuration_migrations/migrations");
const ai_context_manager_1 = require("../common/chat/ai_context_manager");
const language_server_feature_state_provider_1 = require("../common/language_server/language_server_feature_state_provider");
const diagnostics_service_1 = require("../common/diagnostics/diagnostics_service");
const extension_state_service_1 = require("../common/state/extension_state_service");
const version_diagnostics_renderer_1 = require("../common/diagnostics/version_diagnostics/version_diagnostics_renderer");
const version_state_provider_1 = require("../common/state/version_state_provider");
const gitlab_instance_version_provider_1 = require("../common/state/gitlab_instance_version_provider");
const feature_state_diagnostics_renderer_1 = require("../common/diagnostics/feature_state_diagnostics/feature_state_diagnostics_renderer");
const extension_configuration_service_1 = require("../common/utils/extension_configuration_service");
const settings_state_provider_1 = require("../common/state/settings_state_provider");
const user_friendly_error_1 = require("../common/errors/user_friendly_error");
const knowledge_graph_webview_1 = require("../common/webview/knowldege_graph/knowledge_graph_webview");
const settings_diagnostics_renderer_1 = require("./diagnostics/settings_diagnostics_renderer");
const openers = __importStar(require("./commands/openers"));
const tokenInput = __importStar(require("./accounts/remove_account"));
const account_service_1 = require("./accounts/account_service");
const extension_state_1 = require("./extension_state");
const fetch_1 = require("./fetch");
const code_suggestions_promo_1 = require("./code_suggestions_promo");
const create_snippet_1 = require("./commands/create_snippet");
const insert_snippet_1 = require("./commands/insert_snippet");
const ci_config_lint_commands_1 = require("./commands/ci_config_lint_commands");
const issuable_controller_1 = require("./issuable_controller");
const issuable_data_provider_1 = require("./tree_view/issuable_data_provider");
const current_branch_data_provider_1 = require("./tree_view/current_branch_data_provider");
const review_file_system_1 = require("./review/review_file_system");
const constants_1 = require("./constants");
const command_names_1 = require("./command_names");
const ci_completion_provider_1 = require("./ci/ci_completion_provider");
const git_extension_wrapper_1 = require("./git/git_extension_wrapper");
const mr_discussion_commands_1 = require("./commands/mr_discussion_commands");
const has_comments_decoration_provider_1 = require("./review/has_comments_decoration_provider");
const change_type_decoration_provider_1 = require("./review/change_type_decoration_provider");
const checkout_mr_branch_1 = require("./commands/checkout_mr_branch");
const open_in_gitlab_1 = require("./commands/open_in_gitlab");
const clone_wiki_1 = require("./commands/clone_wiki");
const create_snippet_patch_1 = require("./commands/create_snippet_patch");
const download_artifact_1 = require("./commands/download_artifact");
const apply_snippet_patch_1 = require("./commands/apply_snippet_patch");
const open_mr_file_1 = require("./commands/open_mr_file");
const gitlab_remote_file_system_1 = require("./remotefs/gitlab_remote_file_system");
const open_repository_1 = require("./commands/open_repository");
const context_utils_1 = require("./utils/context_utils");
const current_branch_refresher_1 = require("./current_branch_refresher");
const status_bar_1 = require("./status_bar");
const run_with_valid_project_1 = require("./commands/run_with_valid_project");
const trigger_pipeline_action_1 = require("./commands/trigger_pipeline_action");
const sidebar_view_state_1 = require("./tree_view/sidebar_view_state");
const gitlab_project_repository_1 = require("./gitlab/gitlab_project_repository");
const select_project_1 = require("./gitlab/select_project");
const selected_project_store_1 = require("./gitlab/selected_project_store");
const search_input_1 = require("./search_input");
const gitlab_uri_handler_1 = require("./gitlab_uri_handler");
const authenticate_command_1 = require("./accounts/authenticate_command");
const gitlab_authentication_provider_1 = require("./accounts/oauth/gitlab_authentication_provider");
const yaml_support_1 = require("./yaml_support");
const job_actions_1 = require("./commands/job_actions");
const merged_yaml_content_provider_1 = require("./ci/merged_yaml_content_provider");
const pipeline_actions_1 = require("./commands/pipeline_actions");
const open_trace_artifact_1 = require("./commands/open_trace_artifact");
const job_log_content_provider_1 = require("./ci/job_log_content_provider");
const save_raw_job_trace_1 = require("./ci/save_raw_job_trace");
const job_log_folding_provider_1 = require("./ci/job_log_folding_provider");
const scroll_to_bottom_command_1 = require("./ci/scroll_to_bottom_command");
const pending_job_webview_controller_1 = require("./ci/pending_job_webview_controller");
const security_finding_controller_1 = require("./ci/security_finding_controller");
const desktop_language_client_factory_1 = require("./language_server/desktop_language_client_factory");
const dependency_container_desktop_1 = require("./dependency_container_desktop");
const validate_accounts_1 = require("./commands/validate_accounts");
const publish_to_gitlab_1 = require("./commands/publish_to_gitlab");
const protected_branch_manager_1 = require("./gitlab/protected_branch_manager");
const remote_security_scans_data_provider_1 = require("./tree_view/remote_security_scans_data_provider");
const workspace_account_manager_1 = require("./accounts/workspace_account_manager");
const select_workspace_account_command_1 = require("./accounts/select_workspace_account_command");
const account_status_bar_item_1 = require("./accounts/account_status_bar_item");
const deselect_workspace_account_command_1 = require("./accounts/deselect_workspace_account_command");
const account_preselection_service_1 = require("./accounts/account_preselection_service");
const token_refresh_service_1 = require("./accounts/token_refresh_service");
const token_exchange_service_1 = require("./gitlab/token_exchange_service");
const open_mcp_config_command_1 = require("./mcp/open_mcp_config_command");
const registerCommands = (context, languageServerManager, workspaceAccountManager) => {
    const authenticateCommand = new authenticate_command_1.AuthenticateCommand(git_extension_wrapper_1.gitExtensionWrapper, account_service_1.accountService);
    const commands = {
        [command_names_1.USER_COMMANDS.SHOW_ISSUES_ASSIGNED_TO_ME]: (0, run_with_valid_project_1.runWithValidProject)(openers.showIssues),
        [command_names_1.USER_COMMANDS.SHOW_MERGE_REQUESTS_ASSIGNED_TO_ME]: (0, run_with_valid_project_1.runWithValidProject)(openers.showMergeRequests),
        [command_names_1.USER_COMMANDS.AUTHENTICATE]: authenticateCommand.run,
        [command_names_1.USER_COMMANDS.REMOVE_ACCOUNT]: tokenInput.removeAccount,
        [command_names_1.USER_COMMANDS.VALIDATE_ACCOUNTS]: validate_accounts_1.validateAccounts,
        [command_names_1.USER_COMMANDS.SELECT_WORKSPACE_ACCOUNT]: (0, select_workspace_account_command_1.createSelectWorkspaceAccountCommand)(workspaceAccountManager),
        [command_names_1.USER_COMMANDS.DESELECT_WORKSPACE_ACCOUNT]: (0, deselect_workspace_account_command_1.createDeselectWorkspaceAccountCommand)(workspaceAccountManager),
        [command_names_1.USER_COMMANDS.OPEN_ACTIVE_FILE]: (0, run_with_valid_project_1.runWithValidProjectFile)(openers.openActiveFile),
        [command_names_1.USER_COMMANDS.COPY_LINK_TO_ACTIVE_FILE]: (0, run_with_valid_project_1.runWithValidProjectFile)(openers.copyLinkToActiveFile),
        [command_names_1.USER_COMMANDS.OPEN_CURRENT_MERGE_REQUEST]: (0, run_with_valid_project_1.runWithValidProject)(openers.openCurrentMergeRequest),
        [command_names_1.USER_COMMANDS.OPEN_CREATE_NEW_ISSUE]: (0, run_with_valid_project_1.runWithValidProject)(openers.openCreateNewIssue),
        [command_names_1.USER_COMMANDS.OPEN_CREATE_NEW_MR]: (0, run_with_valid_project_1.runWithValidProject)(openers.openCreateNewMr),
        [command_names_1.USER_COMMANDS.OPEN_PROJECT_PAGE]: (0, run_with_valid_project_1.runWithValidProject)(openers.openProjectPage),
        [command_names_1.USER_COMMANDS.PIPELINE_ACTIONS]: (0, run_with_valid_project_1.runWithValidProject)(trigger_pipeline_action_1.triggerPipelineAction),
        [command_names_1.USER_COMMANDS.ISSUE_SEARCH]: (0, run_with_valid_project_1.runWithValidProject)(search_input_1.showIssueSearchInput),
        [command_names_1.USER_COMMANDS.MERGE_REQUEST_SEARCH]: (0, run_with_valid_project_1.runWithValidProject)(search_input_1.showMergeRequestSearchInput),
        [command_names_1.USER_COMMANDS.ADVANCED_SEARCH]: (0, run_with_valid_project_1.runWithValidProject)(search_input_1.showAdvancedSearchInput),
        [command_names_1.USER_COMMANDS.COMPARE_CURRENT_BRANCH]: (0, run_with_valid_project_1.runWithValidProject)(openers.compareCurrentBranch),
        [command_names_1.USER_COMMANDS.CREATE_SNIPPET]: (0, run_with_valid_project_1.runWithValidProject)(create_snippet_1.createSnippet),
        [command_names_1.USER_COMMANDS.INSERT_SNIPPET]: (0, run_with_valid_project_1.runWithValidProject)(insert_snippet_1.insertSnippet),
        [command_names_1.USER_COMMANDS.VALIDATE_CI_CONFIG]: (0, run_with_valid_project_1.runWithValidProject)(ci_config_lint_commands_1.validateCiConfig),
        [command_names_1.USER_COMMANDS.SHOW_MERGED_CI_CONFIG]: (0, run_with_valid_project_1.runWithValidProject)(ci_config_lint_commands_1.showMergedCiConfig),
        [command_names_1.PROGRAMMATIC_COMMANDS.SHOW_RICH_CONTENT]: issuable_controller_1.issuableController.open.bind(issuable_controller_1.issuableController),
        [command_names_1.USER_COMMANDS.RESOLVE_THREAD]: mr_discussion_commands_1.toggleResolved,
        [command_names_1.USER_COMMANDS.UNRESOLVE_THREAD]: mr_discussion_commands_1.toggleResolved,
        [command_names_1.USER_COMMANDS.DELETE_COMMENT]: mr_discussion_commands_1.deleteComment,
        [command_names_1.USER_COMMANDS.START_EDITING_COMMENT]: mr_discussion_commands_1.editComment,
        [command_names_1.USER_COMMANDS.CANCEL_EDITING_COMMENT]: mr_discussion_commands_1.cancelEdit,
        [command_names_1.USER_COMMANDS.SUBMIT_COMMENT_EDIT]: mr_discussion_commands_1.submitEdit,
        [command_names_1.USER_COMMANDS.CREATE_COMMENT]: mr_discussion_commands_1.createComment,
        [command_names_1.USER_COMMANDS.CHECKOUT_MR_BRANCH]: checkout_mr_branch_1.checkoutMrBranch,
        [command_names_1.USER_COMMANDS.OPEN_IN_GITLAB]: open_in_gitlab_1.openInGitLab,
        [command_names_1.USER_COMMANDS.COPY_LINK_TO_CLIPBOARD]: open_in_gitlab_1.copyLinkToClipboard,
        [command_names_1.USER_COMMANDS.CLONE_WIKI]: clone_wiki_1.cloneWiki,
        [command_names_1.USER_COMMANDS.CREATE_SNIPPET_PATCH]: (0, run_with_valid_project_1.runWithValidProject)(create_snippet_patch_1.createSnippetPatch),
        [command_names_1.USER_COMMANDS.APPLY_SNIPPET_PATCH]: (0, run_with_valid_project_1.runWithValidProject)(apply_snippet_patch_1.applySnippetPatch),
        [command_names_1.USER_COMMANDS.CANCEL_FAILED_COMMENT]: mr_discussion_commands_1.cancelFailedComment,
        [command_names_1.USER_COMMANDS.RETRY_FAILED_COMMENT]: mr_discussion_commands_1.retryFailedComment,
        [command_names_1.USER_COMMANDS.OPEN_REPOSITORY]: open_repository_1.openRepository,
        [command_names_1.USER_COMMANDS.SIDEBAR_VIEW_AS_LIST]: () => (0, sidebar_view_state_1.setSidebarViewState)(sidebar_view_state_1.SidebarViewState.ListView),
        [command_names_1.USER_COMMANDS.SIDEBAR_VIEW_AS_TREE]: () => (0, sidebar_view_state_1.setSidebarViewState)(sidebar_view_state_1.SidebarViewState.TreeView),
        [command_names_1.USER_COMMANDS.REFRESH_SIDEBAR]: async () => {
            await account_service_1.accountService.reloadCache();
            await (0, gitlab_project_repository_1.getProjectRepository)().reload();
            await current_branch_refresher_1.currentBranchRefresher.refresh(true);
        },
        [command_names_1.USER_COMMANDS.OPEN_MR_FILE]: open_mr_file_1.openMrFile,
        [command_names_1.USER_COMMANDS.SELECT_PROJECT_FOR_REPOSITORY]: select_project_1.selectProjectForRepository,
        [command_names_1.USER_COMMANDS.SELECT_PROJECT]: select_project_1.selectProjectCommand,
        [command_names_1.USER_COMMANDS.ASSIGN_PROJECT]: select_project_1.assignProject,
        [command_names_1.USER_COMMANDS.CLEAR_SELECTED_PROJECT]: select_project_1.clearSelectedProjects,
        [command_names_1.USER_COMMANDS.DOWNLOAD_ARTIFACTS]: download_artifact_1.downloadArtifacts,
        [command_names_1.USER_COMMANDS.OPEN_TRACE_ARTIFACT]: open_trace_artifact_1.openTraceArtifact,
        [command_names_1.USER_COMMANDS.WAIT_FOR_PENDING_JOB]: async (item) => {
            await pending_job_webview_controller_1.pendingWebviewController.waitForPendingJob(item);
        },
        [command_names_1.USER_COMMANDS.VIEW_SECURITY_FINDING]: async (item, projectInRepository) => {
            await security_finding_controller_1.securityFindingWebviewController.open(item, projectInRepository);
        },
        [command_names_1.USER_COMMANDS.PUBLISH_TO_GITLAB]: publish_to_gitlab_1.publishToGitlab,
        [command_names_1.USER_COMMANDS.SAVE_RAW_JOB_TRACE]: save_raw_job_trace_1.saveRawJobTrace,
        [command_names_1.USER_COMMANDS.SCROLL_TO_BOTTOM]: scroll_to_bottom_command_1.scrollToBottom,
        [command_names_1.USER_COMMANDS.EXECUTE_JOB]: job_actions_1.executeJob,
        [command_names_1.USER_COMMANDS.RETRY_JOB]: job_actions_1.retryJob,
        [command_names_1.USER_COMMANDS.CANCEL_JOB]: job_actions_1.cancelJob,
        [command_names_1.USER_COMMANDS.RETRY_FAILED_PIPELINE_JOBS]: pipeline_actions_1.retryPipeline,
        [command_names_1.USER_COMMANDS.CANCEL_PIPELINE]: pipeline_actions_1.cancelPipeline,
        // this command is not exposed in the browser version
        // TODO implement this command for the browser version
        [command_names_1.USER_COMMANDS.RESTART_LANGUAGE_SERVER]: async () => {
            if (!languageServerManager) {
                return;
            }
            await languageServerManager.restartLanguageServer();
        },
        [command_names_1.PROGRAMMATIC_COMMANDS.SHOW_ERROR_MESSAGE]: openers.showErrorMessage,
        [command_names_1.USER_COMMANDS.MCP_OPEN_USER_CONFIG]: open_mcp_config_command_1.openMcpUserConfigCommand,
        [command_names_1.USER_COMMANDS.SHOW_KNOWLEDGE_GRAPH]: async () => {
            await vscode_1.default.commands.executeCommand(knowledge_graph_webview_1.SHOW_KNOWLEDGE_GRAPH_WEBVIEW_COMMAND);
        },
    };
    Object.keys(commands).forEach(cmd => {
        context.subscriptions.push(vscode_1.default.commands.registerCommand(cmd, (0, wrap_command_with_catch_1.wrapCommandWithCatch)(commands[cmd])));
    });
};
const registerCiCompletion = (context) => {
    const subscription = vscode_1.default.languages.registerCompletionItemProvider({ pattern: '**/*.gitlab-ci*.{yml,yaml}' }, new ci_completion_provider_1.CiCompletionProvider(), '$', '{');
    context.subscriptions.push(subscription);
};
const activateDebugMode = () => {
    const installSourceMapsIfDebug = () => {
        if (extension_configuration_service_1.extensionConfigurationService.getConfiguration().debug)
            (0, source_map_support_1.install)();
    };
    installSourceMapsIfDebug();
    vscode_1.default.workspace.onDidChangeConfiguration(e => {
        if (e.affectsConfiguration(extension_configuration_1.GITLAB_DEBUG_MODE))
            installSourceMapsIfDebug();
    });
};
/**
 * @param {vscode.ExtensionContext} context
 */
const activate = async (context) => {
    const outputChannel = vscode_1.default.window.createOutputChannel('GitLab Workflow');
    (0, log_1.initializeLogging)(line => outputChannel.appendLine(line));
    await (0, migrations_1.runExtensionConfigurationMigrations)();
    activateDebugMode();
    (0, fetch_1.initializeNetworkAgent)();
    context_utils_1.contextUtils.init(context);
    vscode_1.default.workspace.registerFileSystemProvider(constants_1.REVIEW_URI_SCHEME, new review_file_system_1.ReviewFileSystem(), review_file_system_1.ReviewFileSystem.OPTIONS);
    vscode_1.default.workspace.registerTextDocumentContentProvider(constants_1.MERGED_YAML_URI_SCHEME, new merged_yaml_content_provider_1.MergedYamlContentProvider());
    const jobLogContentProvider = new job_log_content_provider_1.JobLogContentProvider(context);
    vscode_1.default.workspace.registerTextDocumentContentProvider(constants_1.JOB_LOG_URI_SCHEME, jobLogContentProvider);
    vscode_1.default.languages.registerFoldingRangeProvider({ scheme: constants_1.JOB_LOG_URI_SCHEME }, new job_log_folding_provider_1.JobLogFoldingProvider());
    vscode_1.default.workspace.registerFileSystemProvider(constants_1.REMOTE_URI_SCHEME, new gitlab_remote_file_system_1.GitLabRemoteFileSystem(), gitlab_remote_file_system_1.GitLabRemoteFileSystem.OPTIONS);
    const dependencyContainer = (0, dependency_container_desktop_1.createDependencyContainer)();
    const extensionStateService = new extension_state_service_1.DefaultExtensionStateService();
    const diagnosticsService = new diagnostics_service_1.DefaultDiagnosticsService(extensionStateService);
    issuable_controller_1.issuableController.init(context);
    await account_service_1.accountService.init(context).catch(e => {
        (0, handle_error_1.handleError)(e);
        throw e;
    });
    (0, workspace_account_manager_1.initWorkspaceAccountManager)(context, account_service_1.accountService);
    context.subscriptions.push(new token_refresh_service_1.TokenRefreshService(account_service_1.accountService, token_exchange_service_1.tokenExchangeService));
    const projectRepository = new gitlab_project_repository_1.GitLabProjectRepositoryImpl(account_service_1.accountService, git_extension_wrapper_1.gitExtensionWrapper, selected_project_store_1.selectedProjectStore, (0, workspace_account_manager_1.getWorkspaceAccountManager)());
    (0, gitlab_project_repository_1.setGlobalProjectRepository)(projectRepository);
    context.subscriptions.push(new account_status_bar_item_1.AccountStatusBarItem((0, workspace_account_manager_1.getWorkspaceAccountManager)()));
    context.subscriptions.push(new account_preselection_service_1.AccountPreselectionService((0, workspace_account_manager_1.getWorkspaceAccountManager)(), git_extension_wrapper_1.gitExtensionWrapper));
    selected_project_store_1.selectedProjectStore.init(context);
    registerCiCompletion(context);
    (0, yaml_support_1.setupYamlSupport)(context);
    (0, code_suggestions_promo_1.setupCodeSuggestionsPromo)(context);
    vscode_1.default.window.registerUriHandler(gitlab_uri_handler_1.gitlabUriHandler);
    context.subscriptions.push(git_extension_wrapper_1.gitExtensionWrapper);
    status_bar_1.statusBar.init();
    context.subscriptions.push(status_bar_1.statusBar);
    context.subscriptions.push(jobLogContentProvider);
    context.subscriptions.push(new protected_branch_manager_1.ProtectedBranchManager(git_extension_wrapper_1.gitExtensionWrapper, (0, gitlab_project_repository_1.getProjectRepository)()));
    vscode_1.default.window.registerFileDecorationProvider(has_comments_decoration_provider_1.hasCommentsDecorationProvider);
    vscode_1.default.window.registerFileDecorationProvider(change_type_decoration_provider_1.changeTypeDecorationProvider);
    vscode_1.default.authentication.registerAuthenticationProvider('gitlab', 'GitLab', new gitlab_authentication_provider_1.GitLabAuthenticationProvider());
    const extensionState = new extension_state_1.ExtensionState((0, workspace_account_manager_1.getWorkspaceAccountManager)());
    (0, extension_state_1.setExtensionStateSingleton)(extensionState);
    await extensionState.init();
    current_branch_refresher_1.currentBranchRefresher.init();
    context.subscriptions.push(current_branch_refresher_1.currentBranchRefresher);
    vscode_1.default.window.registerTreeDataProvider('issuesAndMrs', new issuable_data_provider_1.IssuableDataProvider(extensionState));
    vscode_1.default.window.registerTreeDataProvider('currentBranchInfo', current_branch_data_provider_1.currentBranchDataProvider);
    (0, run_with_valid_project_1.registerRepositoryRootProvider)(issuable_controller_1.issuableController, pending_job_webview_controller_1.pendingWebviewController, security_finding_controller_1.securityFindingWebviewController);
    if ((0, local_feature_flag_service_1.getLocalFeatureFlagService)().isEnabled(local_feature_flag_service_1.FeatureFlag.RemoteSecurityScans)) {
        const remoteSecurityScansDataProvider = new remote_security_scans_data_provider_1.RemoteSecurityScansDataProvider();
        vscode_1.default.window.registerTreeDataProvider('remoteSecurityScanning', remoteSecurityScansDataProvider);
        context.subscriptions.push(remoteSecurityScansDataProvider);
    }
    let languageServerManager;
    const languageServerFeatureStateProvider = new language_server_feature_state_provider_1.LanguageServerFeatureStateProvider();
    // context manager without LS is the default
    let aiContextManager = new ai_context_manager_1.DefaultAIContextManager();
    if ((0, local_feature_flag_service_1.getLocalFeatureFlagService)().isEnabled(local_feature_flag_service_1.FeatureFlag.LanguageServer)) {
        try {
            const webviewMessageRegistry = new webview_1.WebviewMessageRegistry();
            languageServerManager = new language_server_manager_1.LanguageServerManager(context, desktop_language_client_factory_1.desktopLanguageClientFactory, dependencyContainer, webviewMessageRegistry, languageServerFeatureStateProvider);
            await languageServerManager.startLanguageServer();
            aiContextManager = new ai_context_manager_1.DefaultAIContextManager(languageServerManager);
            context.subscriptions.push(await (0, webview_1.setupWebviews)(languageServerManager, webviewMessageRegistry, aiContextManager));
        }
        catch (e) {
            (0, handle_error_1.handleError)(new user_friendly_error_1.UserFriendlyError('GitLab Language Server failed to start', e));
        }
    }
    else {
        const codeSuggestions = new code_suggestions_1.CodeSuggestions(context, dependencyContainer.gitLabPlatformManager);
        await codeSuggestions.init();
        context.subscriptions.push(codeSuggestions);
    }
    const glVersionProvider = new gitlab_instance_version_provider_1.GitLabInstanceVersionProvider(dependencyContainer.gitLabPlatformManager);
    extensionStateService.addStateProvider(version_diagnostics_renderer_1.VersionDetailsStateKey, new version_state_provider_1.VersionStateProvider(context.extension.packageJSON.version, languageServerManager, glVersionProvider));
    diagnosticsService.addRenderer(new version_diagnostics_renderer_1.VersionDiagnosticsRenderer());
    extensionStateService.addStateProvider(language_server_feature_state_provider_1.LanguageServerFeatureStateKey, languageServerFeatureStateProvider);
    diagnosticsService.addRenderer(new feature_state_diagnostics_renderer_1.FeatureStateDiagnosticsRenderer());
    extensionStateService.addStateProvider(settings_state_provider_1.SettingsDetailsStateKey, new settings_state_provider_1.SettingsStateProvider(extension_configuration_service_1.extensionConfigurationService));
    diagnosticsService.addRenderer(new settings_diagnostics_renderer_1.DesktopSettingsStateDiagnosticsRenderer());
    registerCommands(context, languageServerManager, (0, workspace_account_manager_1.getWorkspaceAccountManager)());
    // we don't want to hold the extension startup by waiting on VS Code and GitLab API
    (0, do_not_await_1.doNotAwait)(Promise.all([
        vscode_1.default.commands.executeCommand(command_names_1.USER_COMMANDS.VALIDATE_ACCOUNTS, true),
        (0, sidebar_view_state_1.setSidebarViewState)(sidebar_view_state_1.SidebarViewState.ListView),
        git_extension_wrapper_1.gitExtensionWrapper.init(),
        projectRepository.init(),
        current_branch_refresher_1.currentBranchRefresher.refresh(),
        pending_job_webview_controller_1.pendingWebviewController.init(context),
        security_finding_controller_1.securityFindingWebviewController.init(context),
        (0, main_1.activateCommon)(context, dependencyContainer, outputChannel, aiContextManager, diagnosticsService, languageServerFeatureStateProvider),
    ]).catch(e => (0, handle_error_1.handleError)(e)));
};
exports.activate = activate;
//# sourceMappingURL=extension.js.map