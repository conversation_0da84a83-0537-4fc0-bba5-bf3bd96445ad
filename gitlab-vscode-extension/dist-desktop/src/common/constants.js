"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LS_WEBVIEW_IDS = exports.IS_OSX = exports.AGENTIC_TABS_WEBVIEW_ID = exports.SECURITY_VULNS_WEBVIEW_ID = exports.KNOWLEDGE_GRAPH_WEBVIEW_ID = exports.AGENTIC_CHAT_WEBVIEW_ID = exports.DUO_CHAT_WEBVIEW_ID = exports.DUO_WORKFLOW_PANEL_WEBVIEW_ID = exports.REQUEST_TIMEOUT_MILLISECONDS = exports.MINIMUM_VERSION = exports.DO_NOT_SHOW_VERSION_WARNING = exports.MINIMUM_CODE_SUGGESTIONS_VERSION = exports.DO_NOT_SHOW_CODE_SUGGESTIONS_VERSION_WARNING = exports.CONFIG_NAMESPACE = exports.GITLAB_COM_URL = void 0;
exports.GITLAB_COM_URL = 'https://gitlab.com';
exports.CONFIG_NAMESPACE = 'gitlab';
exports.DO_NOT_SHOW_CODE_SUGGESTIONS_VERSION_WARNING = 'DO_NOT_SHOW_VERSION_WARNING';
// NOTE: This needs to _always_ be a 3 digits
exports.MINIMUM_CODE_SUGGESTIONS_VERSION = '16.8.0';
exports.DO_NOT_SHOW_VERSION_WARNING = 'DO_NOT_SHOW_VERSION_WARNING';
// NOTE: This needs to _always_ be a 3 digits
exports.MINIMUM_VERSION = '16.1.0';
exports.REQUEST_TIMEOUT_MILLISECONDS = 25000;
// Webview IDs
exports.DUO_WORKFLOW_PANEL_WEBVIEW_ID = 'duo-workflow-panel';
exports.DUO_CHAT_WEBVIEW_ID = 'duo-chat-v2';
exports.AGENTIC_CHAT_WEBVIEW_ID = 'agentic-duo-chat';
exports.KNOWLEDGE_GRAPH_WEBVIEW_ID = 'knowledge-graph';
exports.SECURITY_VULNS_WEBVIEW_ID = 'security-vuln-details';
exports.AGENTIC_TABS_WEBVIEW_ID = 'agentic-tabs';
// Environment
// TODO: Remove process reference in common constants
// https://gitlab.com/gitlab-org/gitlab-vscode-extension/-/issues/1615
exports.IS_OSX = typeof process !== 'undefined' && process.platform === 'darwin';
exports.LS_WEBVIEW_IDS = [
    exports.DUO_WORKFLOW_PANEL_WEBVIEW_ID,
    exports.DUO_CHAT_WEBVIEW_ID,
    exports.AGENTIC_CHAT_WEBVIEW_ID,
    exports.AGENTIC_TABS_WEBVIEW_ID,
];
//# sourceMappingURL=constants.js.map