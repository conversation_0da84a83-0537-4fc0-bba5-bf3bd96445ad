"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_FETCH_RESPONSE = void 0;
exports.DEFAULT_FETCH_RESPONSE = '# Fabulous Project\n\nThis project does fabulous things.';
const DEFAULT_JSON_RESPONSE = {
    name: 'Fabulous Project',
    description: 'This project does fabulous things.',
};
const fn = jest.fn().mockResolvedValue({
    ok: true,
    async arrayBuffer() {
        return Buffer.from(exports.DEFAULT_FETCH_RESPONSE);
    },
    async text() {
        return exports.DEFAULT_FETCH_RESPONSE;
    },
    async json() {
        return DEFAULT_JSON_RESPONSE;
    },
});
// eslint-disable-next-line import/no-default-export
exports.default = fn;
//# sourceMappingURL=fetch_logged.js.map