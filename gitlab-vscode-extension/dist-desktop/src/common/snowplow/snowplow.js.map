{"version": 3, "file": "snowplow.js", "sourceRoot": "", "sources": ["../../../../src/common/snowplow/snowplow.ts"], "names": [], "mappings": ";;;;;;AAAA,yDAQgC;AAChC,8DAAgC;AAChC,+BAAoC;AACpC,gCAA6B;AAC7B,uCAAoC;AAGpC;;;;GAIG;AACH,SAAS,cAAc,CAAC,OAAgB;IACtC,MAAM,kBAAkB,GAA2B,EAAE,CAAC;IAEtD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QACjC,kBAAkB,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;IAEH,kBAAkB,CAAC,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC;IAEzD,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AAED,MAAa,QAAQ;IACnB,QAAQ,CAAU;IAElB,QAAQ,CAAkB;IAE1B,QAAQ,CAAc;IAEtB,oBAAoB,CAAqB;IAEzC,gDAAgD;IAChD,MAAM,CAAC,SAAS,CAAW;IAE3B,4CAA4C;IAC5C,gDAAgD;IAChD,YAAoB,OAAwB;QAC1C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,mBAAmB,CAAC;QACxD,IAAI,CAAC,QAAQ,GAAG,IAAI,iBAAO,CACzB,IAAI,CAAC,QAAQ,CAAC,YAAY,EAC1B,IAAI,CAAC,QAAQ,CAAC,QAAQ,EACtB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAC3B,CAAC;QACF,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACtB,IAAI,CAAC,QAAQ,GAAG,IAAA,0BAAW,EAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACnF,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,OAAyB;QAC1C,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;YACrD,CAAC;YACD,MAAM,EAAE,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC;YACjC,QAAQ,CAAC,SAAS,GAAG,EAAE,CAAC;QAC1B,CAAC;QAED,OAAO,QAAQ,CAAC,SAAS,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,MAAwB;QACvC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;YAC7B,OAAO;QACT,CAAC;QAED,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,qCAAqC,CAAC;QAC3E,MAAM,QAAQ,GAAG,MAAM,IAAA,qBAAK,EAAC,GAAG,EAAE;YAChC,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;aACnC;YACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gBACnB,MAAM,EAAE,mEAAmE;gBAC3E,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;oBACvB,MAAM,OAAO,GAAG,IAAA,SAAM,GAAE,CAAC;oBACzB,mEAAmE;oBACnE,8HAA8H;oBAC9H,6DAA6D;oBAC7D,2EAA2E;oBAC3E,8GAA8G;oBAC9G,4EAA4E;oBAC5E,+CAA+C;oBAC/C,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;oBAC1B,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;oBACtB,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;oBAC7B,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;oBACvB,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBAEtC,OAAO,cAAc,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;gBACvC,CAAC,CAAC;aACH,CAAC;SACH,CAAC,CAAC;QAEH,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;YAC5B,SAAG,CAAC,IAAI,CAAC,8CAA8C,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,KAAsB,EACtB,UAAoE;QAEpE,MAAM,OAAO,GACX,UAAU;YACV,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;gBACjB,IAAI,CAAC,KAAK,uBAAuB,EAAE,CAAC;oBAClC,OAAO,IAAI,CAAC,oBAAoB,CAAC;gBACnC,CAAC;gBACD,OAAO,CAAC,CAAC;YACX,CAAC,CAAC,CAAC;QACL,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAA,+BAAgB,EAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;IACxD,CAAC;IAED,KAAK,CAAC,IAAI;QACR,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;IAC7B,CAAC;CACF;AA9FD,4BA8FC"}