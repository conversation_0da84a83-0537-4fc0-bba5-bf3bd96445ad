{"version": 3, "file": "emitter.js", "sourceRoot": "", "sources": ["../../../../src/common/snowplow/emitter.ts"], "names": [], "mappings": ";;;AAIA,IAAK,YAIJ;AAJD,WAAK,YAAY;IACf,qDAAO,CAAA;IACP,uDAAQ,CAAA;IACR,qDAAO,CAAA;AACT,CAAC,EAJI,YAAY,KAAZ,YAAY,QAIhB;AAED,MAAa,OAAO;IAClB,cAAc,GAAqB,EAAE,CAAC;IAEtC,SAAS,CAAoB;IAE7B,aAAa,CAAS;IAEtB,SAAS,CAAS;IAElB,aAAa,CAAe;IAE5B,QAAQ,CAA6B;IAErC,YAAY,YAAoB,EAAE,QAAgB,EAAE,QAA2B;QAC7E,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC,OAAO,CAAC;IAC5C,CAAC;IAED,GAAG,CAAC,IAAoB;QACtB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE/B,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjD,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW;QACf,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnC,MAAM,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC5D,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;YACzB,MAAM,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED,KAAK;QACH,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,KAAK,IAAI,EAAE;YACpC,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YAEzB,IAAI,IAAI,CAAC,aAAa,KAAK,YAAY,CAAC,QAAQ,EAAE,CAAC;gBACjD,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QACvB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC,OAAO,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC,QAAQ,CAAC;QAC3C,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC9B,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;QAC1B,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;IAC3B,CAAC;CACF;AAvDD,0BAuDC"}