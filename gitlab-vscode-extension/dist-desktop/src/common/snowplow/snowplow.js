"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Snowplow = void 0;
const tracker_core_1 = require("@snowplow/tracker-core");
const cross_fetch_1 = __importDefault(require("cross-fetch"));
const uuid_1 = require("uuid");
const log_1 = require("../log");
const emitter_1 = require("./emitter");
/**
 * Adds the 'stm' paramater with the current time to the payload
 * Stringyfiy all payload values
 * @param payload - The payload which will be mutated
 */
function preparePayload(payload) {
    const stringifiedPayload = {};
    Object.keys(payload).forEach(key => {
        stringifiedPayload[key] = String(payload[key]);
    });
    stringifiedPayload.stm = new Date().getTime().toString();
    return stringifiedPayload;
}
class Snowplow {
    #emitter;
    #options;
    #tracker;
    #ideExtensionContext;
    // eslint-disable-next-line no-use-before-define
    static #instance;
    // constructors can't be made private with #
    // eslint-disable-next-line no-restricted-syntax
    constructor(options) {
        this.#options = options;
        this.#ideExtensionContext = options.ideExtensionContext;
        this.#emitter = new emitter_1.Emitter(this.#options.timeInterval, this.#options.maxItems, this.#sendEvent.bind(this));
        this.#emitter.start();
        this.#tracker = (0, tracker_core_1.trackerCore)({ callback: this.#emitter.add.bind(this.#emitter) });
    }
    static getInstance(options) {
        if (!this.#instance) {
            if (!options) {
                throw new Error('Snowplow should be instantiated');
            }
            const sp = new Snowplow(options);
            Snowplow.#instance = sp;
        }
        return Snowplow.#instance;
    }
    async #sendEvent(events) {
        if (!this.#options.enabled()) {
            return;
        }
        const url = `${this.#options.endpoint}/com.snowplowanalytics.snowplow/tp2`;
        const response = await (0, cross_fetch_1.default)(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                schema: 'iglu:com.snowplowanalytics.snowplow/payload_data/jsonschema/1-0-4',
                data: events.map(event => {
                    const eventId = (0, uuid_1.v4)();
                    // All values prefieled below are part of snowplow tracker protocol
                    // https://docs.snowplow.io/docs/collecting-data/collecting-from-own-applications/snowplow-tracker-protocol/#common-parameters
                    // Values are set according to either common GitLab standard:
                    // tna - representing tracker namespace and being set across GitLab to "gl"
                    // tv - represents tracker value, to make it aligned with downstream system it has to be prefixed with "js-*""
                    // aid - represents app Id is configured via options to gitlab_ide_extension
                    // eid - represents uuid for each emitted event
                    event.add('eid', eventId);
                    event.add('p', 'app');
                    event.add('tv', 'js-gitlab');
                    event.add('tna', 'gl');
                    event.add('aid', this.#options.appId);
                    return preparePayload(event.build());
                }),
            }),
        });
        if (response.status !== 200) {
            log_1.log.warn(`Could not send telmetry to snowplow status=${response.status}`);
        }
    }
    async trackStructEvent(event, contextArg) {
        const context = contextArg &&
            contextArg.map(x => {
                if (x === 'ide-extension-context') {
                    return this.#ideExtensionContext;
                }
                return x;
            });
        this.#tracker.track((0, tracker_core_1.buildStructEvent)(event), context);
    }
    async stop() {
        await this.#emitter.stop();
    }
}
exports.Snowplow = Snowplow;
//# sourceMappingURL=snowplow.js.map