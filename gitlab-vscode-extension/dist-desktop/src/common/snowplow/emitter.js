"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Emitter = void 0;
var EmitterState;
(function (EmitterState) {
    EmitterState[EmitterState["STARTED"] = 0] = "STARTED";
    EmitterState[EmitterState["STOPPING"] = 1] = "STOPPING";
    EmitterState[EmitterState["STOPPED"] = 2] = "STOPPED";
})(EmitterState || (EmitterState = {}));
class Emitter {
    #trackingQueue = [];
    #callback;
    #timeInterval;
    #maxItems;
    #currentState;
    #timeout;
    constructor(timeInterval, maxItems, callback) {
        this.#maxItems = maxItems;
        this.#timeInterval = timeInterval;
        this.#callback = callback;
        this.#currentState = EmitterState.STOPPED;
    }
    add(data) {
        this.#trackingQueue.push(data);
        if (this.#trackingQueue.length >= this.#maxItems) {
            this.#drainQueue().catch(() => { });
        }
    }
    async #drainQueue() {
        if (this.#trackingQueue.length > 0) {
            const copyOfTrackingQueue = this.#trackingQueue.map(e => e);
            this.#trackingQueue = [];
            await this.#callback(copyOfTrackingQueue);
        }
    }
    start() {
        this.#timeout = setTimeout(async () => {
            await this.#drainQueue();
            if (this.#currentState !== EmitterState.STOPPING) {
                this.start();
            }
        }, this.#timeInterval);
        this.#currentState = EmitterState.STARTED;
    }
    async stop() {
        this.#currentState = EmitterState.STOPPING;
        if (this.#timeout) {
            clearTimeout(this.#timeout);
        }
        this.#timeout = undefined;
        await this.#drainQueue();
    }
}
exports.Emitter = Emitter;
//# sourceMappingURL=emitter.js.map