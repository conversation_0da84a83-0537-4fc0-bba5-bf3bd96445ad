"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const log_1 = require("./log");
const extension_configuration_service_1 = require("./utils/extension_configuration_service");
jest.mock('./utils/extension_configuration_service');
describe('logging', () => {
    afterEach(() => {
        expect.hasAssertions();
    });
    let logFunction;
    beforeEach(() => {
        logFunction = jest.fn();
        (0, log_1.initializeLogging)(logFunction);
    });
    const getLoggedMessage = () => logFunction.mock.calls[0][0];
    describe('log', () => {
        beforeEach(() => {
            jest
                .spyOn(extension_configuration_service_1.extensionConfigurationService, 'getConfiguration')
                .mockReturnValue({ debug: true });
        });
        it('passes the argument to the handler', () => {
            const message = 'A very bad error occurred';
            log_1.log.info(message);
            expect(logFunction).toBeCalledTimes(1);
            expect(getLoggedMessage()).toContain(`[info]: ${message}`);
        });
        it.each `
      methodName | logLevel
      ${'debug'} | ${'debug'}
      ${'info'}  | ${'info'}
      ${'warn'}  | ${'warning'}
      ${'error'} | ${'error'}
    `('it handles log level "$logLevel"', ({ methodName, logLevel }) => {
            log_1.log[methodName]('message');
            expect(getLoggedMessage()).toContain(`[${logLevel}]: message`);
        });
        it('does not log debug messages if debug mode is disabled', () => {
            jest
                .spyOn(extension_configuration_service_1.extensionConfigurationService, 'getConfiguration')
                .mockReturnValue({ debug: false });
            log_1.log.debug('message');
            expect(logFunction).not.toBeCalled();
        });
        it('indents multiline messages', () => {
            log_1.log.error('error happened\nand the next line\nexplains why');
            expect(getLoggedMessage()).toContain(`[error]: error happened\n    and the next line\n    explains why`);
        });
    });
    describe('log Error', () => {
        describe('for normal errors', () => {
            it('passes the argument to the handler', () => {
                const message = 'A very bad error occurred';
                const error = {
                    message,
                    stack: 'stack',
                };
                log_1.log.error(error);
                expect(getLoggedMessage()).toMatch(/\[error\]: A very bad error occurred\s+stack/m);
            });
        });
        describe('for detailed errors', () => {
            it('passes the details to the handler', () => {
                const message = 'Could not fetch from GitLab: error 404';
                log_1.log.error({
                    details: { message },
                });
                const logFunctionArgument = logFunction.mock.calls[0][0];
                expect(logFunctionArgument).toMatch(/\[error\]:/);
                expect(logFunctionArgument).toMatch(message);
            });
        });
    });
});
//# sourceMappingURL=log.test.js.map