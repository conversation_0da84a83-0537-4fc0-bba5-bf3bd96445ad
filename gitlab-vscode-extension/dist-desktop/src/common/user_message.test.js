"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const vscode_1 = __importDefault(require("vscode"));
const in_memory_memento_1 = require("../../test/integration/test_infrastructure/in_memory_memento");
const user_message_1 = require("./user_message");
describe('UserMessage', () => {
    const TEST_STORAGE_KEY = 'test.message.key';
    const TEST_MESSAGE = 'Test message';
    let globalState;
    let message;
    let callbackSpy;
    let messageResponse;
    beforeEach(() => {
        globalState = new in_memory_memento_1.InMemoryMemento();
        callbackSpy = jest.fn();
        jest
            .mocked(vscode_1.default.window.showInformationMessage)
            .mockImplementation(async () => messageResponse);
        message = new user_message_1.UserMessage(globalState, TEST_STORAGE_KEY, TEST_MESSAGE, [
            { title: 'Action 1', callback: callbackSpy },
            { title: 'Action 2', callback: () => { } },
        ]);
    });
    it('shows message with all options', async () => {
        await message.trigger();
        expect(vscode_1.default.window.showInformationMessage).toHaveBeenCalledWith(TEST_MESSAGE, 'Action 1', 'Action 2', "Don't show again");
    });
    it('executes callback when action is selected', async () => {
        messageResponse = 'Action 1';
        await message.trigger();
        expect(callbackSpy).toHaveBeenCalled();
    });
    it('stores dismissal in globalState when "Don\'t show again" is selected', async () => {
        messageResponse = "Don't show again";
        await message.trigger();
        expect(globalState.get(TEST_STORAGE_KEY)).toBe(true);
    });
    it('does not show message if previously dismissed', async () => {
        await globalState.update(TEST_STORAGE_KEY, true);
        await message.trigger();
        expect(vscode_1.default.window.showInformationMessage).not.toHaveBeenCalled();
    });
    it('shows message only once per session', async () => {
        await message.trigger();
        await message.trigger();
        expect(vscode_1.default.window.showInformationMessage).toHaveBeenCalledTimes(1);
    });
    it('does nothing when no option is selected', async () => {
        messageResponse = undefined;
        await message.trigger();
        expect(callbackSpy).not.toHaveBeenCalled();
        expect(globalState.get(TEST_STORAGE_KEY)).toBeUndefined();
    });
});
//# sourceMappingURL=user_message.test.js.map