"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.log = exports.initializeLogging = void 0;
const dayjs_1 = __importDefault(require("dayjs"));
const common_1 = require("./errors/common");
const json_1 = require("./utils/json");
const extension_configuration_service_1 = require("./utils/extension_configuration_service");
// eslint-disable-next-line no-console
let globalLog = console.error;
const initializeLogging = (logLine) => {
    globalLog = logLine;
};
exports.initializeLogging = initializeLogging;
const LOG_LEVEL = {
    DEBUG: 'debug',
    INFO: 'info',
    WARNING: 'warning',
    ERROR: 'error',
};
// pad subsequent lines by 4 spaces
const PADDING = 4;
const multilineLog = (line, level) => {
    const prefix = `${(0, dayjs_1.default)().format('YYYY-MM-DDTHH:mm:ss:SSS')} [${level}]: `;
    const padNextLines = (text) => text.replace(/\n/g, `\n${' '.repeat(PADDING)}`);
    globalLog(`${prefix}${padNextLines(line)}`);
};
const formatError = (e) => (0, common_1.isDetailedError)(e) ? (0, json_1.prettyJson)(e.details) : `${e.message}\n${e.stack}`;
const logWithLevel = (level, a1, a2) => {
    if (typeof a1 === 'string') {
        const errorText = a2 ? `\n${formatError(a2)}` : '';
        multilineLog(`${a1}${errorText}`, level);
    }
    else {
        multilineLog(formatError(a1), level);
    }
};
/** This method logs only if user added `"debug": true` to their `settings.json` */
const debug = (a1, a2) => {
    const config = extension_configuration_service_1.extensionConfigurationService.getConfiguration();
    if (config.debug)
        logWithLevel(LOG_LEVEL.DEBUG, a1, a2);
};
const info = (a1, a2) => logWithLevel(LOG_LEVEL.INFO, a1, a2);
const warn = (a1, a2) => logWithLevel(LOG_LEVEL.WARNING, a1, a2);
const error = (a1, a2) => logWithLevel(LOG_LEVEL.ERROR, a1, a2);
exports.log = { debug, info, warn, error };
//# sourceMappingURL=log.js.map