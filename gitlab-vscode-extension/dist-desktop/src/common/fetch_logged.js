"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const cross_fetch_1 = __importDefault(require("cross-fetch"));
const log_1 = require("./log");
const extract_url_1 = require("./utils/extract_url");
async function fetchLogged(input, init) {
    const start = Date.now();
    const url = (0, extract_url_1.extractURL)(input);
    const method = init?.method || 'GET';
    const headers = init?.headers || {};
    const verboseLogging = process.env.VSCODE_GITLAB_VERBOSE_LOGGING === 'true';
    // Log request details if verbose logging is enabled
    if (verboseLogging) {
        log_1.log.debug(`fetch: ${method} ${url}`);
        log_1.log.debug(`fetch: headers: ${JSON.stringify(headers)}`);
        if (init?.body && typeof init.body === 'string') {
            try {
                const bodyObj = JSON.parse(init.body);
                log_1.log.debug(`fetch: request body: ${JSON.stringify(bodyObj)}`);
            }
            catch {
                log_1.log.debug(`fetch: request body (raw): ${init.body}`);
            }
        }
    }
    try {
        const resp = await (0, cross_fetch_1.default)(input, init);
        const duration = Date.now() - start;
        log_1.log.debug(`fetch: request to ${url} returned HTTP ${resp.status} after ${duration} ms`);
        // Log response details if verbose logging is enabled
        if (verboseLogging) {
            const responseHeaders = {};
            resp.headers.forEach((value, key) => {
                responseHeaders[key] = value;
            });
            log_1.log.debug(`fetch: response headers: ${JSON.stringify(responseHeaders)}`);
            // Clone response to read body without consuming it
            const respClone = resp.clone();
            try {
                const responseText = await respClone.text();
                if (responseText) {
                    try {
                        const responseObj = JSON.parse(responseText);
                        log_1.log.debug(`fetch: response body: ${JSON.stringify(responseObj)}`);
                    }
                    catch {
                        const truncatedText = responseText.substring(0, 500) + (responseText.length > 500 ? '...' : '');
                        log_1.log.debug(`fetch: response body (raw): ${truncatedText}`);
                    }
                }
            }
            catch (bodyError) {
                log_1.log.debug(`fetch: could not read response body: ${bodyError instanceof Error ? bodyError.message : String(bodyError)}`);
            }
        }
        return resp;
    }
    catch (e) {
        const duration = Date.now() - start;
        log_1.log.debug(`fetch: request to ${url} threw an exception after ${duration} ms`);
        log_1.log.debug(`fetch: request to ${url} failed with:`, e);
        throw e;
    }
}
// eslint-disable-next-line import/no-default-export
exports.default = fetchLogged;
//# sourceMappingURL=fetch_logged.js.map