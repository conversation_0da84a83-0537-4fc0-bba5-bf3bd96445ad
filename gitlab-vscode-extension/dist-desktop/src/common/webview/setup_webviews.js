"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupWebviews = void 0;
const vscode = __importStar(require("vscode"));
const constants_1 = require("../constants");
const ls_webview_controller_1 = require("./ls_webview_controller");
const create_theme_handler_middleware_1 = require("./theme/create_theme_handler_middleware");
const create_initial_state_middleware_1 = require("./theme/create_initial_state_middleware");
const get_ls_webview_content_1 = require("./get_ls_webview_content");
const middleware_1 = require("./middleware");
const duo_chat_handlers_1 = require("./duo_chat/duo_chat_handlers");
const duo_chat_commands_1 = require("./duo_chat/duo_chat_commands");
const duo_agentic_chat_commands_1 = require("./duo_agentic_chat/duo_agentic_chat_commands");
const duo_chat_controller_1 = require("./duo_chat/duo_chat_controller");
const knowledge_graph_handler_1 = require("./knowldege_graph/knowledge_graph_handler");
const knowledge_graph_webview_1 = require("./knowldege_graph/knowledge_graph_webview");
const CHAT_WEBVIEW_IDS = [constants_1.DUO_CHAT_WEBVIEW_ID, constants_1.AGENTIC_CHAT_WEBVIEW_ID];
// webviews that show in the VS Code panels, sidebar or other custom views (like activity bar)
const PANEL_WEBVIEW_IDS = [
    ...CHAT_WEBVIEW_IDS,
    constants_1.DUO_WORKFLOW_PANEL_WEBVIEW_ID,
    constants_1.AGENTIC_TABS_WEBVIEW_ID,
];
// webviews in the editor area (i.e. tabs)
const EDITOR_WEBVIEW_IDS = [constants_1.SECURITY_VULNS_WEBVIEW_ID];
// custom settings for webviews in the editor area
const EDITOR_WEBVIEW_SETTINGS = {
    [constants_1.SECURITY_VULNS_WEBVIEW_ID]: {
        viewColumn: vscode.ViewColumn.Beside,
    },
};
// converts webview kebab-case strings to camelCase for commands
const kebabToCamelCase = (str) => str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
const setupKnowledgeGraphWebview = async (webviewMessageRegistry) => {
    const webview = new knowledge_graph_webview_1.KnowledgeGraphWebview();
    await (0, knowledge_graph_handler_1.registerKnowledgeGraphHandlers)(webviewMessageRegistry, webview);
    return new vscode.Disposable(() => webview.dispose());
};
const setupEditorWebview = async (webviewInfo, themePublisher) => {
    const commandId = `gl.webview.${kebabToCamelCase(webviewInfo.id)}.show`;
    let panel;
    const disposables = [];
    disposables.push(vscode.commands.registerCommand(`${commandId}`, async (initState = {}) => {
        const viewColumn = EDITOR_WEBVIEW_SETTINGS?.[webviewInfo.id]?.viewColumn ?? vscode.ViewColumn.One;
        const initialWorkspaceConfig = {};
        if (panel && Object.keys(initState).length === 0) {
            panel.reveal(viewColumn);
        }
        else {
            if (panel) {
                panel.dispose();
            }
            const initialState = {
                ...initState,
                ...initialWorkspaceConfig,
            };
            panel = vscode.window.createWebviewPanel(webviewInfo.id, webviewInfo.title, {
                viewColumn,
                preserveFocus: true,
            }, {
                enableScripts: true,
                retainContextWhenHidden: true,
            });
            panel.webview.html = await (0, get_ls_webview_content_1.getWebviewContent)(new URL(webviewInfo.uris[0]), // FIXME this is not the right way to pick the uri, this should be platform dependent
            webviewInfo.title);
            const middlewares = [
                (0, create_theme_handler_middleware_1.createThemeHandlerMiddleware)(themePublisher),
                (0, create_initial_state_middleware_1.createInitialStateMiddleware)(themePublisher, initialState),
            ];
            (0, middleware_1.applyMiddleware)(panel, middlewares);
            panel.onDidDispose(() => {
                panel = undefined;
            });
        }
        return panel;
    }));
    return new vscode.Disposable(() => disposables.forEach(x => x.dispose()));
};
const setupPanelWebview = async (webviewInfo, themePublisher, webviewMessageRegistry, aiContextManager) => {
    const viewId = `gl.webview.${webviewInfo.id}`;
    const commandId = `${kebabToCamelCase(viewId)}.show`;
    const middlewares = [(0, create_theme_handler_middleware_1.createThemeHandlerMiddleware)(themePublisher)];
    const controllerParams = {
        viewId,
        url: new URL(webviewInfo.uris[0]), // FIXME this is not the right way to pick the uri, this should be platform dependent
        title: webviewInfo.title,
        middlewares,
    };
    const controller = CHAT_WEBVIEW_IDS.includes(webviewInfo.id)
        ? new duo_chat_controller_1.LSDuoChatWebviewController(controllerParams)
        : new ls_webview_controller_1.LsWebviewController(controllerParams);
    const disposables = [];
    // FIXME: we should have a mechanism to restore webview state because VS Code prefers to destroy the web pages when they are not visible (the `retainContextWhenHidden` should be false)
    disposables.push(vscode.window.registerWebviewViewProvider(viewId, controller, {
        webviewOptions: { retainContextWhenHidden: true },
    }));
    disposables.push(vscode.commands.registerCommand(`${commandId}`, async () => {
        await controller.show();
    }));
    if (CHAT_WEBVIEW_IDS.includes(webviewInfo.id)) {
        (0, duo_chat_handlers_1.registerDuoChatHandlers)(webviewMessageRegistry, controller, webviewInfo.id);
    }
    if (webviewInfo.id === constants_1.DUO_CHAT_WEBVIEW_ID) {
        disposables.push(await (0, duo_chat_commands_1.registerDuoChatCommands)(webviewMessageRegistry, controller, aiContextManager));
    }
    if (webviewInfo.id === constants_1.AGENTIC_CHAT_WEBVIEW_ID) {
        disposables.push(await (0, duo_agentic_chat_commands_1.registerDuoAgenticChatCommands)(webviewMessageRegistry, controller));
    }
    return new vscode.Disposable(() => disposables.forEach(x => x.dispose()));
};
const setupWebviews = async (webviewManager, webviewMessageRegistry, aiContextManager) => {
    const allInfos = await webviewManager.getWebviewInfos();
    const panelWebviewInfos = allInfos.filter(i => PANEL_WEBVIEW_IDS.includes(i.id));
    const editorWebviewInfos = allInfos.filter(i => EDITOR_WEBVIEW_IDS.includes(i.id));
    const disposables = await Promise.all([
        ...panelWebviewInfos.map(webviewInfo => setupPanelWebview(webviewInfo, webviewManager, webviewMessageRegistry, aiContextManager)),
        ...editorWebviewInfos.map(webviewInfo => setupEditorWebview(webviewInfo, webviewManager)),
        setupKnowledgeGraphWebview(webviewMessageRegistry),
    ]);
    return new vscode.Disposable(() => disposables.forEach(x => x.dispose()));
};
exports.setupWebviews = setupWebviews;
//# sourceMappingURL=setup_webviews.js.map