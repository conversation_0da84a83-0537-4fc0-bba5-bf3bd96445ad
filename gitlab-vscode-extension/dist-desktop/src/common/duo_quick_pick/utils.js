"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateDuoDiagnosticsStatusItem = exports.generateCodeSuggestionsLangToggleItem = exports.generateCodeSuggestionsToggleItem = exports.generateDuoChatStatusItem = exports.generateDuoUnavailableStatusItem = exports.generateCodeSuggestionsStatusItem = exports.generateQuickPickItem = void 0;
const vscode_1 = __importDefault(require("vscode"));
const constants_1 = require("../constants");
const extension_configuration_1 = require("../utils/extension_configuration");
const code_suggestions_state_manager_1 = require("../code_suggestions/code_suggestions_state_manager");
const command_names_1 = require("../command_names");
const constants_2 = require("./constants");
const generateQuickPickItem = (label, description) => ({
    label,
    description,
});
exports.generateQuickPickItem = generateQuickPickItem;
const getCurrentFileLanguage = () => {
    const activeEditor = vscode_1.default.window.activeTextEditor;
    return activeEditor ? activeEditor.document.languageId : undefined;
};
const isLanguageEnabled = (language) => {
    const { enabledSupportedLanguages, additionalLanguages } = (0, extension_configuration_1.getDuoCodeSuggestionsConfiguration)();
    return enabledSupportedLanguages[language] || additionalLanguages.includes(language);
};
const generateCodeSuggestionsStatusItem = (globallyEnabled) => {
    let isEnabled = globallyEnabled;
    const language = getCurrentFileLanguage();
    if (language) {
        isEnabled = globallyEnabled && isLanguageEnabled(language);
    }
    const label = isEnabled ? constants_2.CODE_SUGGESTIONS_ENABLED : constants_2.CODE_SUGGESTIONS_DISABLED;
    return (0, exports.generateQuickPickItem)(label, constants_2.CODE_SUGGESTIONS_DESCRIPTION);
};
exports.generateCodeSuggestionsStatusItem = generateCodeSuggestionsStatusItem;
const authenticateUser = () => vscode_1.default.commands.executeCommand(command_names_1.USER_COMMANDS.AUTHENTICATE);
const generateDuoUnavailableStatusItem = (state) => {
    switch (state) {
        case code_suggestions_state_manager_1.VisibleCodeSuggestionsState.NO_ACCOUNT:
            return [(0, exports.generateQuickPickItem)(constants_2.DUO_UNAVAILABLE, constants_2.NOT_AUTHENTICATED), () => authenticateUser()];
        default:
            return [(0, exports.generateQuickPickItem)(constants_2.DUO_UNAVAILABLE), () => { }];
    }
};
exports.generateDuoUnavailableStatusItem = generateDuoUnavailableStatusItem;
const generateDuoChatStatusItem = () => {
    const workspaceConfig = vscode_1.default.workspace.getConfiguration(constants_1.CONFIG_NAMESPACE);
    const label = workspaceConfig?.duoChat?.enabled ? constants_2.DUO_CHAT_ENABLED : constants_2.DUO_CHAT_DISABLED;
    return (0, exports.generateQuickPickItem)(label);
};
exports.generateDuoChatStatusItem = generateDuoChatStatusItem;
const generateCodeSuggestionsToggleItem = (enabled) => {
    const label = enabled ? constants_2.DISABLE_CODE_SUGGESTIONS : constants_2.ENABLE_CODE_SUGGESTIONS;
    return (0, exports.generateQuickPickItem)(label);
};
exports.generateCodeSuggestionsToggleItem = generateCodeSuggestionsToggleItem;
const generateCodeSuggestionsLangToggleItem = (globallyEnabled) => {
    let quickPickItem;
    const language = getCurrentFileLanguage();
    if (globallyEnabled && language) {
        const action = isLanguageEnabled(language) ? constants_2.DISABLE_CODE_SUGGESTIONS : constants_2.ENABLE_CODE_SUGGESTIONS;
        const label = `${action} for ${language}`;
        quickPickItem = (0, exports.generateQuickPickItem)(label);
    }
    return quickPickItem;
};
exports.generateCodeSuggestionsLangToggleItem = generateCodeSuggestionsLangToggleItem;
const generateDuoDiagnosticsStatusItem = (state) => {
    if (state === code_suggestions_state_manager_1.VisibleCodeSuggestionsState.NO_LICENSE) {
        const label = '$(error) Status: 1 problem detected, contact your GitLab administrator.';
        const description = 'Duo license not assigned.';
        return (0, exports.generateQuickPickItem)(label, description);
    }
    if (state === code_suggestions_state_manager_1.VisibleCodeSuggestionsState.ERROR ||
        state === code_suggestions_state_manager_1.VisibleCodeSuggestionsState.SUGGESTIONS_API_ERROR) {
        const label = '$(error) Status: Code Suggestion requests to API are failing.';
        const description = 'See logs for more details.';
        return (0, exports.generateQuickPickItem)(label, description);
    }
    return (0, exports.generateQuickPickItem)(constants_2.DUO_STATUS_ZERO_PROBLEMS_DETECTED);
};
exports.generateDuoDiagnosticsStatusItem = generateDuoDiagnosticsStatusItem;
//# sourceMappingURL=utils.js.map