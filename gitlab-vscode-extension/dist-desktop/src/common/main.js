"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.activateCommon = void 0;
const vscode = __importStar(require("vscode"));
const instance_feature_flag_service_1 = require("./feature_flags/instance_feature_flag_service");
const code_suggestion_accepted_1 = require("./code_suggestions/commands/code_suggestion_accepted");
const toggle_language_1 = require("./code_suggestions/commands/toggle_language");
const show_output_command_1 = require("./show_output_command");
const gitlab_chat_1 = require("./chat/gitlab_chat");
const setup_telemetry_1 = require("./snowplow/setup_telemetry");
const check_version_1 = require("./gitlab/check_version");
const command_names_1 = require("./command_names");
const diagnostics_command_1 = require("./diagnostics/diagnostics_command");
const diagnostics_document_provider_1 = require("./diagnostics/diagnostics_document_provider");
const duo_tutorial_1 = require("./code_suggestions/commands/duo_tutorial");
const activateCommon = async (context, container, outputChannel, aiContextManager, diagnosticsService, languageServerFeatureStateProvider) => {
    (0, setup_telemetry_1.setupTelemetry)(context, container.gitLabTelemetryEnvironment);
    const featureFlagService = new instance_feature_flag_service_1.InstanceFeatureFlagService(container.gitLabPlatformManager);
    // FIXME: we probably don't have to call this because at the time we activate the service there are no accounts yet, the account service will be initializing a bit later
    await featureFlagService.init();
    context.subscriptions.push((0, check_version_1.setupVersionCheck)(container.gitLabPlatformManager, context), featureFlagService);
    vscode.workspace.registerTextDocumentContentProvider('gitlab-diagnostics', new diagnostics_document_provider_1.DiagnosticsDocumentProvider(diagnosticsService));
    const commands = {
        [command_names_1.USER_COMMANDS.SHOW_LOGS]: (0, show_output_command_1.createShowOutputCommand)(outputChannel),
        [command_names_1.USER_COMMANDS.SHOW_DIAGNOSTICS]: diagnostics_command_1.diagnosticsCommand,
        [code_suggestion_accepted_1.COMMAND_CODE_SUGGESTION_ACCEPTED]: code_suggestion_accepted_1.codeSuggestionAccepted,
        [toggle_language_1.COMMAND_TOGGLE_CODE_SUGGESTIONS_FOR_LANGUAGE]: toggle_language_1.toggleCodeSuggestionsForLanguage,
        [command_names_1.USER_COMMANDS.DUO_TUTORIAL]: duo_tutorial_1.duoTutorial,
        [command_names_1.USER_COMMANDS.SHOW_DIAGNOSTICS_FROM_SIDE_PANEL]: diagnostics_command_1.diagnosticsCommand,
    };
    Object.entries(commands).forEach(([cmdName, cmd]) => {
        context.subscriptions.push(vscode.commands.registerCommand(cmdName, cmd));
    });
    await (0, gitlab_chat_1.activateChat)(context, container.gitLabPlatformManager, aiContextManager, languageServerFeatureStateProvider);
};
exports.activateCommon = activateCommon;
//# sourceMappingURL=main.js.map