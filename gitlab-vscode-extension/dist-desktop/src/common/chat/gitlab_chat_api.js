"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GitLabChatApi = exports.AI_MESSAGES_QUERY_17_5_AND_LATER = exports.AI_MESSAGES_QUERY = exports.CHAT_INPUT_TEMPLATE_17_10_AND_LATER = exports.CHAT_INPUT_TEMPLATE_17_5_AND_LATER = exports.CHAT_INPUT_TEMPLATE_17_3_AND_LATER = exports.CHAT_INPUT_TEMPLATE_17_2_AND_EARLIER = exports.ConversationType = exports.MINIMUM_CONVERSATION_TYPE_VERSION = exports.MINIMUM_ADDITIONAL_CONTEXT_FIELD_VERSION = exports.MINIMUM_PLATFORM_ORIGIN_FIELD_VERSION = void 0;
const graphql_request_1 = require("graphql-request");
const ai_completion_response_channel_1 = require("../api/graphql/ai_completion_response_channel");
const log_1 = require("../log");
const check_version_1 = require("../gitlab/check_version");
const if_version_gte_1 = require("../utils/if_version_gte");
const get_current_user_1 = require("../gitlab/api/get_current_user");
const pulling_1 = require("./api/pulling");
const constants_1 = require("./constants");
exports.MINIMUM_PLATFORM_ORIGIN_FIELD_VERSION = '17.3.0';
exports.MINIMUM_ADDITIONAL_CONTEXT_FIELD_VERSION = '17.5.0-pre';
exports.MINIMUM_CONVERSATION_TYPE_VERSION = '17.10.0-pre';
// https://docs.gitlab.com/api/graphql/reference/#aiconversationsthreadsconversationtype
// threadId is required to retrieve messages for DUO_QUICK_CHAT and DUO_CHAT conversation types
var ConversationType;
(function (ConversationType) {
    ConversationType["DUO_CHAT_LEGACY"] = "DUO_CHAT_LEGACY";
    ConversationType["DUO_CHAT"] = "DUO_CHAT";
    ConversationType["DUO_QUICK_CHAT"] = "DUO_QUICK_CHAT";
})(ConversationType || (exports.ConversationType = ConversationType = {}));
exports.CHAT_INPUT_TEMPLATE_17_2_AND_EARLIER = {
    query: (0, graphql_request_1.gql) `
    mutation chat(
      $question: String!
      $resourceId: AiModelID
      $currentFileContext: AiCurrentFileInput
      $clientSubscriptionId: String
    ) {
      aiAction(
        input: {
          chat: { resourceId: $resourceId, content: $question, currentFile: $currentFileContext }
          clientSubscriptionId: $clientSubscriptionId
        }
      ) {
        requestId
        errors
      }
    }
  `,
    defaultVariables: {},
};
exports.CHAT_INPUT_TEMPLATE_17_3_AND_LATER = {
    query: (0, graphql_request_1.gql) `
    mutation chat(
      $question: String!
      $resourceId: AiModelID
      $currentFileContext: AiCurrentFileInput
      $clientSubscriptionId: String
      $platformOrigin: String!
    ) {
      aiAction(
        input: {
          chat: { resourceId: $resourceId, content: $question, currentFile: $currentFileContext }
          clientSubscriptionId: $clientSubscriptionId
          platformOrigin: $platformOrigin
        }
      ) {
        requestId
        errors
      }
    }
  `,
    defaultVariables: {
        platformOrigin: constants_1.PLATFORM_ORIGIN,
    },
};
exports.CHAT_INPUT_TEMPLATE_17_5_AND_LATER = {
    query: (0, graphql_request_1.gql) `
    mutation chat(
      $question: String!
      $resourceId: AiModelID
      $currentFileContext: AiCurrentFileInput
      $clientSubscriptionId: String
      $platformOrigin: String!
      $additionalContext: [AiAdditionalContextInput!]
    ) {
      aiAction(
        input: {
          chat: {
            resourceId: $resourceId
            content: $question
            currentFile: $currentFileContext
            additionalContext: $additionalContext
          }
          clientSubscriptionId: $clientSubscriptionId
          platformOrigin: $platformOrigin
        }
      ) {
        requestId
        errors
      }
    }
  `,
    defaultVariables: {
        platformOrigin: constants_1.PLATFORM_ORIGIN,
    },
};
exports.CHAT_INPUT_TEMPLATE_17_10_AND_LATER = {
    query: (0, graphql_request_1.gql) `
    mutation chat(
      $question: String!
      $resourceId: AiModelID
      $currentFileContext: AiCurrentFileInput
      $clientSubscriptionId: String
      $platformOrigin: String!
      $additionalContext: [AiAdditionalContextInput!]
      $conversationType: AiConversationsThreadsConversationType
      $threadId: AiConversationThreadID
    ) {
      aiAction(
        input: {
          chat: {
            resourceId: $resourceId
            content: $question
            currentFile: $currentFileContext
            additionalContext: $additionalContext
          }
          clientSubscriptionId: $clientSubscriptionId
          platformOrigin: $platformOrigin
          conversationType: $conversationType
          threadId: $threadId
        }
      ) {
        requestId
        errors
        threadId
      }
    }
  `,
    defaultVariables: {
        platformOrigin: constants_1.PLATFORM_ORIGIN,
    },
};
exports.AI_MESSAGES_QUERY = (0, graphql_request_1.gql) `
  query getAiMessages($requestIds: [ID!], $roles: [AiMessageRole!]) {
    aiMessages(requestIds: $requestIds, roles: $roles) {
      nodes {
        requestId
        role
        content
        contentHtml
        timestamp
        errors
        extras {
          sources
        }
      }
    }
  }
`;
// TODO: update this query to include additional context
// https://gitlab.com/gitlab-org/gitlab/-/issues/489304
exports.AI_MESSAGES_QUERY_17_5_AND_LATER = (0, graphql_request_1.gql) `
  query getAiMessages($requestIds: [ID!], $roles: [AiMessageRole!]) {
    aiMessages(requestIds: $requestIds, roles: $roles) {
      nodes {
        requestId
        role
        content
        contentHtml
        timestamp
        errors
        extras {
          sources
          additionalContext {
            id
            category
            metadata
          }
        }
      }
    }
  }
`;
const errorResponse = (requestId, errors) => ({
    requestId,
    errors,
    role: 'system',
    type: 'error',
});
const successResponse = (response) => ({
    type: 'message',
    ...response,
});
class GitLabChatApi {
    #cachedActionMutation = undefined;
    #cachedMessagesQuery = undefined;
    #canceledPromptRequestIds;
    #manager;
    #aiContextManager;
    constructor(manager, canceledPromptRequestIds, aiContextManager) {
        this.#manager = manager;
        this.#canceledPromptRequestIds = canceledPromptRequestIds;
        this.#aiContextManager = aiContextManager;
    }
    async processNewUserPrompt(question, subscriptionId, currentFileContext, aiContextItems, conversationType, threadId) {
        return this.#sendAiAction({
            question,
            currentFileContext,
            additionalContext: aiContextItems?.map(item => ({
                id: item.id,
                // GraphQL expects uppercase category, e.g. 'FILE' and 'SNIPPET', internally we use lowercase 'file' and 'snippet'
                category: item.category.toUpperCase(),
                // we can safely assume that the content will be populated, by this point
                content: item.content ?? '',
                metadata: item.metadata,
            })),
            clientSubscriptionId: subscriptionId,
            threadId,
            conversationType,
        });
    }
    async pullAiMessage(requestId, role) {
        const response = await (0, pulling_1.pullHandler)(() => this.#getAiMessage(requestId, role));
        if (!response)
            return errorResponse(requestId, ['Reached timeout while fetching response.']);
        return successResponse(response);
    }
    async clearChat(threadId) {
        return this.#sendAiAction({ question: constants_1.SPECIAL_MESSAGES.CLEAR, threadId });
    }
    async resetChat(threadId) {
        return this.#sendAiAction({ question: constants_1.SPECIAL_MESSAGES.RESET, threadId });
    }
    async #currentPlatform() {
        const platform = await this.#manager.getGitLabPlatform();
        if (!platform)
            throw new Error('Platform is missing!');
        return platform;
    }
    async #getAiMessage(requestId, role) {
        const platform = await this.#currentPlatform();
        const query = await this.#messagesQuery();
        const request = {
            type: 'graphql',
            query,
            variables: { requestIds: [requestId], roles: [role.toUpperCase()] },
        };
        const history = await platform.fetchFromApi(request);
        return history.aiMessages.nodes[0];
    }
    async subscribeToUpdates(messageCallback, subscriptionId) {
        const [platform, additionalContextEnabled] = await Promise.all([
            this.#currentPlatform(),
            this.#aiContextManager.isAdditionalContextEnabled(),
        ]);
        const currentUser = await platform.fetchFromApi(get_current_user_1.currentUserRequest);
        log_1.log.debug(`GitLabChatApi: subscribeToUpdates, additionalContextEnabled: ${additionalContextEnabled}`);
        const channel = new ai_completion_response_channel_1.AiCompletionResponseChannel({
            htmlResponse: false,
            userId: `gid://gitlab/User/${currentUser.id}`,
            aiAction: 'CHAT',
            clientSubscriptionId: subscriptionId,
        }, additionalContextEnabled);
        const cable = await platform.connectToCable();
        // we use this flag to fix https://gitlab.com/gitlab-org/gitlab-vscode-extension/-/issues/1397
        // sometimes a chunk comes after the full message and it broke the chat
        let fullMessageReceived = false;
        channel.on('newChunk', async (msg) => {
            if (fullMessageReceived) {
                log_1.log.info(`CHAT-DEBUG: full message received, ignoring chunk`);
                return;
            }
            if (this.#canceledPromptRequestIds.includes(msg.requestId)) {
                log_1.log.info(`CHAT-DEBUG: stream cancelled, ignoring chunk`);
                return;
            }
            await messageCallback(msg);
        });
        channel.on('fullMessage', async (message) => {
            fullMessageReceived = true;
            if (this.#canceledPromptRequestIds.includes(message.requestId)) {
                log_1.log.info(`CHAT-DEBUG: stream cancelled, ignoring full message`);
                cable.disconnect();
                return;
            }
            await messageCallback(message);
            if (subscriptionId) {
                cable.disconnect();
            }
        });
        cable.subscribe(channel);
        return cable;
    }
    async #sendAiAction(variables) {
        const platform = await this.#currentPlatform();
        const { query, defaultVariables } = await this.#actionMutation();
        const projectGqlId = await this.#manager.getProjectGqlId();
        const request = {
            type: 'graphql',
            query,
            variables: {
                ...variables,
                ...defaultVariables,
                resourceId: projectGqlId ?? null,
            },
        };
        return platform.fetchFromApi(request);
    }
    async #actionMutation() {
        if (!this.#cachedActionMutation) {
            const platform = await this.#currentPlatform();
            try {
                const [{ version }, isAdditionalContextEnabled] = await Promise.all([
                    platform.fetchFromApi(check_version_1.versionRequest),
                    this.#aiContextManager.isAdditionalContextEnabled(),
                ]);
                const settings = {
                    version,
                    isAdditionalContextEnabled,
                    actionMutation: (0, if_version_gte_1.ifVersionGte)(version, exports.MINIMUM_CONVERSATION_TYPE_VERSION, () => exports.CHAT_INPUT_TEMPLATE_17_10_AND_LATER, () => (0, if_version_gte_1.ifVersionGte)(version, exports.MINIMUM_PLATFORM_ORIGIN_FIELD_VERSION, () => isAdditionalContextEnabled
                        ? exports.CHAT_INPUT_TEMPLATE_17_5_AND_LATER
                        : exports.CHAT_INPUT_TEMPLATE_17_3_AND_LATER, () => exports.CHAT_INPUT_TEMPLATE_17_2_AND_EARLIER)),
                };
                this.#cachedActionMutation = settings.actionMutation;
                log_1.log.debug(`GitLabChatApi: action mutation settings: ${JSON.stringify(settings, null, 2)}`);
            }
            catch (e) {
                log_1.log.debug(`GitLab version check for sending chat failed:`, e);
                this.#cachedActionMutation = exports.CHAT_INPUT_TEMPLATE_17_3_AND_LATER;
            }
        }
        return this.#cachedActionMutation;
    }
    async #messagesQuery() {
        if (!this.#cachedMessagesQuery) {
            const platform = await this.#currentPlatform();
            try {
                const [{ version }, isAdditionalContextEnabled] = await Promise.all([
                    platform.fetchFromApi(check_version_1.versionRequest),
                    this.#aiContextManager.isAdditionalContextEnabled(),
                ]);
                const settings = {
                    version,
                    isAdditionalContextEnabled,
                    messagesQuery: (0, if_version_gte_1.ifVersionGte)(version, exports.MINIMUM_ADDITIONAL_CONTEXT_FIELD_VERSION, () => exports.AI_MESSAGES_QUERY_17_5_AND_LATER, () => exports.AI_MESSAGES_QUERY),
                };
                this.#cachedMessagesQuery = settings.messagesQuery;
                log_1.log.debug(`GitChatApi: messages query settings: ${JSON.stringify(settings, null, 2)}`);
            }
            catch (e) {
                log_1.log.debug(`GitLab version check for sending chat failed:`, e);
                this.#cachedMessagesQuery = exports.AI_MESSAGES_QUERY;
            }
        }
        return this.#cachedMessagesQuery;
    }
}
exports.GitLabChatApi = GitLabChatApi;
//# sourceMappingURL=gitlab_chat_api.js.map