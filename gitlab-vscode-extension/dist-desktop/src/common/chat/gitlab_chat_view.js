"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GitLabChatView = exports.CHAT_SIDEBAR_VIEW_ID = void 0;
const vscode = __importStar(require("vscode"));
const lodash_1 = require("lodash");
const wait_for_webview_1 = require("../utils/webviews/wait_for_webview");
const log_1 = require("../log");
const prepare_webview_source_1 = require("../utils/webviews/prepare_webview_source");
const constants_1 = require("../constants");
const gitlab_chat_slash_commands_1 = require("./gitlab_chat_slash_commands");
const error_screen_1 = require("./error_screen");
exports.CHAT_SIDEBAR_VIEW_ID = 'gl.chatView';
class GitLabChatView {
    #context;
    #chatView;
    #messageEmitter = new vscode.EventEmitter();
    onViewMessage = this.#messageEmitter.event;
    #visibilityEmitter = new vscode.EventEmitter();
    onDidBecomeVisible = this.#visibilityEmitter.event;
    constructor(context) {
        this.#context = context;
    }
    async resolveWebviewView(webviewView) {
        this.#chatView = webviewView;
        this.#chatView.webview.options = {
            enableScripts: true,
        };
        const initialState = {
            slashCommands: gitlab_chat_slash_commands_1.defaultSlashCommands,
        };
        this.#chatView.webview.html = await (0, prepare_webview_source_1.prepareWebviewSource)(this.#chatView.webview, this.#context, 'gitlab_duo_chat', initialState);
        const timeoutSecondsSetting = vscode.workspace
            .getConfiguration(constants_1.CONFIG_NAMESPACE)
            .get('webviewTimeoutSeconds');
        const timeoutMs = (0, lodash_1.isNumber)(timeoutSecondsSetting) ? timeoutSecondsSetting * 1000 : undefined;
        if (timeoutMs)
            log_1.log.info(`Setting Duo Chat webview timeout to ${timeoutMs}ms`);
        await (0, wait_for_webview_1.waitForWebview)(this.#chatView.webview, timeoutMs);
        this.#chatView.webview.onDidReceiveMessage(m => this.#messageEmitter.fire(m));
        this.#chatView.onDidChangeVisibility(() => {
            if (this.#chatView?.visible)
                this.#visibilityEmitter.fire();
        });
        this.#chatView.onDidDispose(() => {
            this.#chatView = undefined;
        }, this);
    }
    setErrorScreenContent(errorContent) {
        if (!this.#chatView) {
            throw new Error('Chat view not initialized.');
        }
        const htmlErrorCont = (0, error_screen_1.getErrorScreenHtml)(errorContent);
        this.#chatView.webview.html = htmlErrorCont;
    }
    async show() {
        if (!this.#chatView) {
            await vscode.commands.executeCommand(`${exports.CHAT_SIDEBAR_VIEW_ID}.focus`);
            return;
        }
        if (this.#chatView.visible) {
            await this.focusChat();
        }
        else {
            this.#chatView.show();
            await (0, wait_for_webview_1.waitForWebview)(this.#chatView.webview);
        }
    }
    async hide() {
        if (this.#chatView?.visible) {
            await vscode.commands.executeCommand('workbench.action.closeSidebar');
        }
    }
    // send message to webview to focus on the chat prompt
    async focusChat() {
        await this.#sendChatViewCommand({ eventType: 'focusChat' });
    }
    async clearChat() {
        await this.#sendChatViewCommand({ eventType: 'clearChat' });
    }
    async cancelPrompt(canceledPromptRequestIds) {
        await this.#sendChatViewCommand({
            eventType: 'cancelPrompt',
            canceledPromptRequestIds,
        });
    }
    async addRecord(record) {
        await this.#sendChatViewCommand({
            eventType: 'newRecord',
            record,
        });
    }
    async updateRecord(record) {
        await this.#sendChatViewCommand({
            eventType: 'updateRecord',
            record,
        });
    }
    async setLoadingState(isLoading) {
        await this.#sendChatViewCommand({
            eventType: 'setLoadingState',
            isLoading,
        });
    }
    async setContextItemCategories(categories) {
        await this.#sendChatViewCommand({
            eventType: 'contextCategoriesResult',
            categories,
        });
    }
    async setCurrentContextItems(items) {
        await this.#sendChatViewCommand({
            eventType: 'contextCurrentItemsResult',
            items,
        });
    }
    async setContextItemSearchResults(results) {
        await this.#sendChatViewCommand({
            eventType: 'contextItemSearchResult',
            results,
        });
    }
    async #sendChatViewCommand(message) {
        if (!this.#chatView) {
            log_1.log.warn('Trying to send webview chat message without a webview.');
            return;
        }
        await this.#chatView.webview.postMessage(message);
    }
    // set vscode context whether the chat is focused or not
    async setChatFocused(isFocused) {
        await vscode.commands.executeCommand('setContext', 'gitlab:chatFocused', isFocused);
    }
}
exports.GitLabChatView = GitLabChatView;
//# sourceMappingURL=gitlab_chat_view.js.map