{"version": 3, "file": "gitlab_chat.js", "sourceRoot": "", "sources": ["../../../../src/common/chat/gitlab_chat.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC,yDAAqD;AACrD,uDAGiC;AACjC,+CAI6B;AAC7B,4FAGqD;AAErD,iFAAqF;AACrF,yEAA8E;AAC9E,iFAA2E;AAC3E,qEAAgE;AAChE,kEAAuF;AACvF,4EAG0C;AAC1C,8DAAkF;AAClF,4EAG0C;AAC1C,kDAAgE;AAChE,mFAA+E;AAC/E,yDAA0D;AAC1D,4DAA+E;AAC/E,6EAAqE;AAErE,6DAAwD;AACxD,oEAA0F;AAC1F,oEAA0F;AAC1F,uDAAkD;AAElD,IAAI,SAAgC,CAAC;AACrC,IAAI,UAAU,GAAG,KAAK,CAAC;AAEvB,MAAM,qBAAqB,GAAG,CAC5B,OAAgC,EAChC,OAA8B,EAC9B,gBAAkC,EAClC,eAAwB,EACxB,EAAE;IACF,IAAI,eAAe,EAAE,CAAC;QACpB,MAAM,sBAAsB,GAAG,IAAI,4DAA4B,CAAC,OAAO,CAAC,CAAC;QACzE,MAAM,oBAAoB,GAAG,IAAI,sDAA6B,EAAE,CAAC;QACjE,MAAM,UAAU,GAAG,IAAI,4CAAmB,CAAC,OAAO,CAAC,CAAC;QACpD,MAAM,iBAAiB,GAAG,IAAI,+CAA0B,CACtD,IAAI,iCAAyB,CAAC,CAAC,2CAAmC,CAAC,CAAC,CACrE,CAAC;QACF,MAAM,GAAG,GAAG,IAAI,+BAAa,CAAC,sBAAsB,EAAE,EAAE,EAAE,gBAAgB,CAAC,CAAC;QAC5E,SAAS,GAAG,IAAI,sBAAS,CAAC,GAAG,EAAE,oBAAoB,EAAE,UAAU,EAAE,iBAAiB,CAAC,CAAC;IACtF,CAAC;SAAM,CAAC;QACN,SAAS,EAAE,OAAO,EAAE,CAAC;QACrB,SAAS,GAAG,SAAS,CAAC;IACxB,CAAC;AACH,CAAC,CAAC;AAEF,MAAM,gBAAgB,GAAG,KAAK,EAC5B,OAAgC,EAChC,OAA8B,EAC9B,gBAAkC,EAClC,EAAE;IACF,MAAM,QAAQ,GAAG,MAAM,IAAA,4CAAkB,EAAC,OAAO,CAAC,CAAC;IACnD,IAAI,UAAU,KAAK,QAAQ;QAAE,OAAO;IACpC,UAAU,GAAG,QAAQ,CAAC;IAEtB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE,sBAAsB,EAAE,QAAQ,CAAC,CAAC;IACrF,qBAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,gBAAgB,EAAE,QAAQ,CAAC,CAAC;AACtE,CAAC,CAAC;AAEK,MAAM,YAAY,GAAG,KAAK,EAC/B,OAAgC,EAChC,OAA8B,EAC9B,gBAAkC,EAClC,kCAAuE,EACvE,EAAE;IACF,IACE,IAAA,uDAA0B,GAAE,CAAC,SAAS,CAAC,wCAAW,CAAC,cAAc,CAAC;QAClE,kCAAkC,EAClC,CAAC;QACD,MAAM,gBAAgB,GAAG,IAAI,qCAAgB,CAAC,kCAAkC,CAAC,CAAC;QAClF,OAAO,CAAC,aAAa,CAAC,IAAI,CACxB,gBAAgB,EAChB,gBAAgB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;YACpC,qBAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,gBAAgB,EAAE,SAAS,CAAC,aAAa,CAAC,CAAC;QACrF,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,MAAM,gBAAgB,CAAC,OAAO,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;QAE3D,OAAO,CAAC,eAAe,CAAC,KAAK,IAAI,EAAE;YACjC,MAAM,gBAAgB,CAAC,OAAO,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;IACL,CAAC;IAED,MAAM,sBAAsB,GAAG,IAAI,4DAA4B,CAAC,OAAO,CAAC,CAAC;IACzE,MAAM,UAAU,GAAG,IAAI,6CAAoB,CAAC,sBAAsB,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;IAE/F,OAAO,CAAC,aAAa,CAAC,IAAI;IACxB,eAAe;IACf,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,uCAAoB,EAAE,UAAU,CAAC,CAC5E,CAAC;IAEF,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;IAEpE,WAAW;IACX,OAAO,CAAC,aAAa,CAAC,IAAI,CACxB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,2CAAwB,EAAE,KAAK,IAAI,EAAE;QACnE,MAAM,IAAA,iCAAc,EAAC,UAAU,CAAC,CAAC;IACnC,CAAC,CAAC,EACF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,qDAA6B,EAAE,KAAK,IAAI,EAAE;QACxE,MAAM,IAAA,2CAAmB,EAAC,UAAU,CAAC,CAAC;IACxC,CAAC,CAAC,EACF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,uCAAsB,EAAE,KAAK,IAAI,EAAE;QACjE,MAAM,IAAA,8BAAa,EAAC,UAAU,CAAC,CAAC;IAClC,CAAC,CAAC,EACF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,qCAAqB,EAAE,KAAK,IAAI,EAAE;QAChE,MAAM,IAAA,4BAAY,EAAC,UAAU,CAAC,CAAC;IACjC,CAAC,CAAC,EACF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,2BAAgB,EAAE,KAAK,IAAI,EAAE;QAC3D,MAAM,IAAA,kBAAO,EAAC,UAAU,CAAC,CAAC;IAC5B,CAAC,CAAC,EACF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,qDAA6B,EAAE,KAAK,IAAI,EAAE;QACxE,MAAM,IAAA,2CAAmB,EAAC,UAAU,CAAC,CAAC;IACxC,CAAC,CAAC,EAEF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,qDAAyC,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;QAC5F,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC3C,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,2BAA2B,CAAC,CAAC;IAC1E,CAAC,CAAC,EACF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAC7B,uDAA2C,EAC3C,qCAA6B,CAC9B,EACD,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,6CAAyB,EAAE,KAAK,IAAI,EAAE;QACpE,MAAM,IAAA,mCAAe,EAAC,UAAU,CAAC,CAAC;IACpC,CAAC,CAAC,EACF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,6CAAyB,EAAE,KAAK,IAAI,EAAE;QACpE,MAAM,IAAA,mCAAe,EAAC,UAAU,CAAC,CAAC;IACpC,CAAC,CAAC,CACH,CAAC;AACJ,CAAC,CAAC;AAvEW,QAAA,YAAY,gBAuEvB"}