"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const vscode = __importStar(require("vscode"));
const create_fake_partial_1 = require("../test_utils/create_fake_partial");
const quick_chat_1 = require("../quick_chat/quick_chat");
const utils_1 = require("../quick_chat/utils");
const local_feature_flag_service_1 = require("../feature_flags/local_feature_flag_service");
const gitlab_chat_1 = require("./gitlab_chat");
const gitlab_chat_controller_1 = require("./gitlab_chat_controller");
const chat_availability_utils_1 = require("./utils/chat_availability_utils");
const chat_state_manager_1 = require("./chat_state_manager");
jest.mock('vscode');
jest.mock('./utils/chat_availability_utils');
jest.mock('../quick_chat/quick_chat');
jest.mock('../feature_flags/local_feature_flag_service');
jest.mock('./chat_state_manager');
describe('activateChat', () => {
    let context;
    let *********************;
    const aiContextManager = (0, create_fake_partial_1.createFakePartial)({});
    const isLanguageServerEnabledMock = jest.fn().mockReturnValue(false);
    const mockChatStateManagerOnChange = jest.fn();
    const mockProjectPlatform = {
        project: { namespaceWithPath: 'group/project' },
    };
    let triggerChatStateManagerChange;
    jest.mocked(chat_state_manager_1.ChatStateManager).mockImplementation(() => (0, create_fake_partial_1.createFakePartial)({
        onChange: mockChatStateManagerOnChange,
    }));
    beforeEach(() => {
        ********************* = (0, create_fake_partial_1.createFakePartial)({
            onAccountChange: jest.fn(handler => handler()),
            getForActiveProject: jest.fn().mockResolvedValue(mockProjectPlatform),
        });
        context = {
            subscriptions: [],
            extensionUri: vscode.Uri.file('/foo/bar'),
        };
        vscode.window.registerWebviewViewProvider = jest.fn();
        vscode.commands.registerCommand = jest
            .fn()
            .mockReturnValueOnce('command1')
            .mockReturnValueOnce('command2')
            .mockReturnValueOnce('command3')
            .mockReturnValueOnce('command4')
            .mockReturnValueOnce('command5')
            .mockReturnValueOnce('command6')
            .mockReturnValueOnce('command7')
            .mockReturnValueOnce('command8')
            .mockReturnValueOnce('command9')
            .mockReturnValueOnce('command10');
        jest
            .mocked(local_feature_flag_service_1.getLocalFeatureFlagService)
            .mockReturnValue((0, create_fake_partial_1.createFakePartial)({ isEnabled: isLanguageServerEnabledMock }));
        mockChatStateManagerOnChange.mockImplementation(_callback => {
            triggerChatStateManagerChange = _callback;
        });
    });
    it('registers view provider', async () => {
        await (0, gitlab_chat_1.activateChat)(context, *********************, aiContextManager);
        expect(vscode.window.registerWebviewViewProvider).toHaveBeenCalledWith('gl.chatView', expect.any(gitlab_chat_controller_1.GitLabChatController));
    });
    it('registers commands', async () => {
        await (0, gitlab_chat_1.activateChat)(context, *********************, aiContextManager);
        expect(vscode.commands.registerCommand).toHaveBeenNthCalledWith(1, 'gl.openChat', expect.any(Function));
        expect(vscode.commands.registerCommand).toHaveBeenNthCalledWith(2, 'gl.explainSelectedCode', expect.any(Function));
        expect(vscode.commands.registerCommand).toHaveBeenNthCalledWith(3, 'gl.generateTests', expect.any(Function));
        expect(vscode.commands.registerCommand).toHaveBeenNthCalledWith(4, 'gl.refactorCode', expect.any(Function));
        expect(vscode.commands.registerCommand).toHaveBeenNthCalledWith(5, 'gl.fixCode', expect.any(Function));
        expect(vscode.commands.registerCommand).toHaveBeenNthCalledWith(6, 'gl.newChatConversation', expect.any(Function));
        expect(vscode.commands.registerCommand).toHaveBeenNthCalledWith(7, 'gl.copyCodeSnippetFromQuickChat', expect.any(Function));
        expect(vscode.commands.registerCommand).toHaveBeenNthCalledWith(8, 'gl.insertCodeSnippetFromQuickChat', utils_1.insertQuickChatSnippetCommand);
        expect(vscode.commands.registerCommand).toHaveBeenNthCalledWith(9, 'gl.closeChat', expect.any(Function));
        expect(vscode.commands.registerCommand).toHaveBeenNthCalledWith(10, 'gl.focusChat', expect.any(Function));
        expect(context.subscriptions[2]).toEqual('command1');
        expect(context.subscriptions[3]).toEqual('command2');
        expect(context.subscriptions[4]).toEqual('command3');
        expect(context.subscriptions[5]).toEqual('command4');
        expect(context.subscriptions[6]).toEqual('command5');
        expect(context.subscriptions[7]).toEqual('command6');
        expect(context.subscriptions[8]).toEqual('command7');
        expect(context.subscriptions[9]).toEqual('command8');
        expect(context.subscriptions[10]).toEqual('command9');
        expect(context.subscriptions[11]).toEqual('command10');
    });
    describe('Chat state', () => {
        describe('when Language Server is enabled ', () => {
            beforeEach(async () => {
                isLanguageServerEnabledMock.mockReturnValue(true);
                const languageServerFeatureStateProvider = (0, create_fake_partial_1.createFakePartial)({});
                await (0, gitlab_chat_1.activateChat)(context, *********************, aiContextManager, languageServerFeatureStateProvider);
            });
            it('should create "ChatStateManager" to handle chat availability', () => {
                expect(chat_state_manager_1.ChatStateManager).toHaveBeenCalled();
            });
            describe('Quick chat', () => {
                it('creates quick chat when chat is available', async () => {
                    triggerChatStateManagerChange((0, create_fake_partial_1.createFakePartial)({
                        chatAvailable: true,
                    }));
                    await (0, gitlab_chat_1.activateChat)(context, *********************, aiContextManager);
                    // Verify QuickChat was created
                    expect(quick_chat_1.QuickChat).toHaveBeenCalled();
                });
                it('disposes quick chat when chat becomes unavailable', async () => {
                    const mockQuickChat = (0, create_fake_partial_1.createFakePartial)({
                        dispose: jest.fn(),
                    });
                    jest.mocked(quick_chat_1.QuickChat).mockReturnValue(mockQuickChat);
                    // First make chat available
                    triggerChatStateManagerChange((0, create_fake_partial_1.createFakePartial)({
                        chatAvailable: true,
                    }));
                    await (0, gitlab_chat_1.activateChat)(context, *********************, aiContextManager);
                    // Then simulate chat state change
                    triggerChatStateManagerChange((0, create_fake_partial_1.createFakePartial)({
                        chatAvailable: false,
                    }));
                    // Verify
                    expect(mockQuickChat.dispose).toHaveBeenCalled();
                });
                it('does not create quick chat if chat is unavailable', async () => {
                    triggerChatStateManagerChange((0, create_fake_partial_1.createFakePartial)({
                        chatAvailable: false,
                    }));
                    await (0, gitlab_chat_1.activateChat)(context, *********************, aiContextManager);
                    expect(quick_chat_1.QuickChat).not.toHaveBeenCalled();
                });
            });
        });
        describe('when Language Server is disabled', () => {
            beforeEach(async () => {
                isLanguageServerEnabledMock.mockReturnValue(false);
                await (0, gitlab_chat_1.activateChat)(context, *********************, aiContextManager);
            });
            it('should not create "ChatStateManager"', () => {
                expect(chat_state_manager_1.ChatStateManager).not.toHaveBeenCalled();
            });
            it('should listen to the account changes', async () => {
                jest.mocked(chat_availability_utils_1.isDuoChatAvailable).mockResolvedValueOnce(true);
                await (0, gitlab_chat_1.activateChat)(context, *********************, aiContextManager);
                expect(*********************.onAccountChange).toHaveBeenCalled();
            });
        });
        describe('Quick chat', () => {
            it('creates quick chat when chat is available', async () => {
                jest.mocked(chat_availability_utils_1.isDuoChatAvailable).mockResolvedValueOnce(true);
                await (0, gitlab_chat_1.activateChat)(context, *********************, aiContextManager);
                // Verify QuickChat was created and subscribed
                expect(context.subscriptions).toContainEqual(expect.objectContaining({
                    dispose: expect.any(Function),
                }));
            });
            it('disposes quick chat when chat becomes unavailable', async () => {
                const mockQuickChat = (0, create_fake_partial_1.createFakePartial)({
                    dispose: jest.fn(),
                });
                jest.mocked(quick_chat_1.QuickChat).mockReturnValue(mockQuickChat);
                // First make chat available
                jest.mocked(chat_availability_utils_1.isDuoChatAvailable).mockResolvedValueOnce(true);
                await (0, gitlab_chat_1.activateChat)(context, *********************, aiContextManager);
                // Then simulate account change making chat unavailable
                jest.mocked(chat_availability_utils_1.isDuoChatAvailable).mockResolvedValueOnce(false);
                await jest.mocked(*********************.onAccountChange).mock.calls[0][0]();
                // Verify
                expect(mockQuickChat.dispose).toHaveBeenCalled();
            });
            it('does not create quick chat if chat is unavailable', async () => {
                jest.mocked(chat_availability_utils_1.isDuoChatAvailable).mockResolvedValueOnce(false);
                await (0, gitlab_chat_1.activateChat)(context, *********************, aiContextManager);
                expect(quick_chat_1.QuickChat).not.toHaveBeenCalled();
            });
        });
    });
    describe('gitlab:chatAvailable', () => {
        it.each([
            [true, true],
            [false, false],
        ])('is %s when isDuoChatAvailable is %s', async (available, expected) => {
            // first set the opposite value
            jest.mocked(chat_availability_utils_1.isDuoChatAvailable).mockResolvedValueOnce(!available);
            await (0, gitlab_chat_1.activateChat)(context, *********************, aiContextManager);
            // then execute the test
            jest.mocked(vscode.commands.executeCommand).mockClear();
            jest.mocked(chat_availability_utils_1.isDuoChatAvailable).mockResolvedValueOnce(available);
            await jest.mocked(*********************.onAccountChange).mock.calls[0][0]();
            expect(vscode.commands.executeCommand).toHaveBeenCalledWith('setContext', 'gitlab:chatAvailable', expected);
        });
    });
});
//# sourceMappingURL=gitlab_chat.test.js.map