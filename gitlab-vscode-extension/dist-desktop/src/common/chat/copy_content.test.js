"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const vscode = __importStar(require("vscode"));
const copy_content_1 = require("./copy_content");
describe('copyContent', () => {
    it('should copy content to clipboard and show success message', async () => {
        const snippet = 'const test = "hello";';
        const writeTextSpy = jest.spyOn(vscode.env.clipboard, 'writeText').mockResolvedValue();
        const showInfoMessageSpy = jest
            .spyOn(vscode.window, 'showInformationMessage')
            .mockResolvedValue(undefined);
        await (0, copy_content_1.copyContent)(snippet);
        expect(writeTextSpy).toHaveBeenCalledWith(snippet);
        expect(showInfoMessageSpy).toHaveBeenCalledWith('Copied to clipboard.');
    });
    it('should show error message when copying fails', async () => {
        const snippet = 'const test = "hello";';
        const error = new Error('Failed to copy');
        jest.spyOn(vscode.env.clipboard, 'writeText').mockRejectedValue(error);
        const showErrorMessageSpy = jest
            .spyOn(vscode.window, 'showErrorMessage')
            .mockResolvedValue(undefined);
        await (0, copy_content_1.copyContent)(snippet);
        expect(showErrorMessageSpy).toHaveBeenCalledWith('Error copying: Failed to copy');
    });
});
//# sourceMappingURL=copy_content.test.js.map