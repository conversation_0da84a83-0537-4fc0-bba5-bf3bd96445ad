"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.activateChat = void 0;
const vscode = __importStar(require("vscode"));
const quick_chat_1 = require("../quick_chat/quick_chat");
const constants_1 = require("../quick_chat/constants");
const utils_1 = require("../quick_chat/utils");
const local_feature_flag_service_1 = require("../feature_flags/local_feature_flag_service");
const comment_thread_service_1 = require("../quick_chat/comment_thread_service");
const response_processor_1 = require("../quick_chat/response_processor");
const quick_chat_gutter_icon_1 = require("../quick_chat/quick_chat_gutter_icon");
const gitlab_chat_controller_1 = require("./gitlab_chat_controller");
const open_gitlab_chat_1 = require("./commands/open_gitlab_chat");
const explain_selected_code_1 = require("./commands/explain_selected_code");
const generate_tests_1 = require("./commands/generate_tests");
const new_chat_conversation_1 = require("./commands/new_chat_conversation");
const fix_code_1 = require("./commands/fix_code");
const get_platform_manager_for_chat_1 = require("./get_platform_manager_for_chat");
const gitlab_chat_view_1 = require("./gitlab_chat_view");
const refactor_code_1 = require("./commands/refactor_code");
const chat_availability_utils_1 = require("./utils/chat_availability_utils");
const chat_state_manager_1 = require("./chat_state_manager");
const close_gitlab_chat_1 = require("./commands/close_gitlab_chat");
const focus_gitlab_chat_1 = require("./commands/focus_gitlab_chat");
const gitlab_chat_api_1 = require("./gitlab_chat_api");
let quickChat;
let chatActive = false;
const setQuickChatAvailable = (context, manager, aiContextManager, isChatAvailable) => {
    if (isChatAvailable) {
        const ********************** = new get_platform_manager_for_chat_1.GitLabPlatformManagerForChat(manager);
        const commentThreadService = new comment_thread_service_1.QuickChatCommentThreadService();
        const gutterIcon = new quick_chat_gutter_icon_1.QuickChatGutterIcon(context);
        const responseProcessor = new response_processor_1.QuickChatResponseProcessor(new utils_1.MarkdownProcessorPipeline([utils_1.addCopyAndInsertButtonsToCodeBlocks]));
        const api = new gitlab_chat_api_1.GitLabChatApi(**********************, [], aiContextManager);
        quickChat = new quick_chat_1.QuickChat(api, commentThreadService, gutterIcon, responseProcessor);
    }
    else {
        quickChat?.dispose();
        quickChat = undefined;
    }
};
const setChatAvailable = async (context, manager, aiContextManager) => {
    const isActive = await (0, chat_availability_utils_1.isDuoChatAvailable)(manager);
    if (chatActive === isActive)
        return;
    chatActive = isActive;
    await vscode.commands.executeCommand('setContext', 'gitlab:chatAvailable', isActive);
    setQuickChatAvailable(context, manager, aiContextManager, isActive);
};
const activateChat = async (context, manager, aiContextManager, languageServerFeatureStateProvider) => {
    if ((0, local_feature_flag_service_1.getLocalFeatureFlagService)().isEnabled(local_feature_flag_service_1.FeatureFlag.LanguageServer) &&
        languageServerFeatureStateProvider) {
        const chatStateManager = new chat_state_manager_1.ChatStateManager(languageServerFeatureStateProvider);
        context.subscriptions.push(chatStateManager, chatStateManager.onChange(chatState => {
            setQuickChatAvailable(context, manager, aiContextManager, chatState.chatAvailable);
        }));
    }
    else {
        await setChatAvailable(context, manager, aiContextManager);
        manager.onAccountChange(async () => {
            await setChatAvailable(context, manager, aiContextManager);
        });
    }
    const ********************** = new get_platform_manager_for_chat_1.GitLabPlatformManagerForChat(manager);
    const controller = new gitlab_chat_controller_1.GitLabChatController(**********************, context, aiContextManager);
    context.subscriptions.push(
    // sidebar view
    vscode.window.registerWebviewViewProvider(gitlab_chat_view_1.CHAT_SIDEBAR_VIEW_ID, controller));
    context.subscriptions.push({ dispose: () => quickChat?.dispose() });
    // commands
    context.subscriptions.push(vscode.commands.registerCommand(open_gitlab_chat_1.COMMAND_OPEN_GITLAB_CHAT, async () => {
        await (0, open_gitlab_chat_1.openGitLabChat)(controller);
    }), vscode.commands.registerCommand(explain_selected_code_1.COMMAND_EXPLAIN_SELECTED_CODE, async () => {
        await (0, explain_selected_code_1.explainSelectedCode)(controller);
    }), vscode.commands.registerCommand(generate_tests_1.COMMAND_GENERATE_TESTS, async () => {
        await (0, generate_tests_1.generateTests)(controller);
    }), vscode.commands.registerCommand(refactor_code_1.COMMAND_REFACTOR_CODE, async () => {
        await (0, refactor_code_1.refactorCode)(controller);
    }), vscode.commands.registerCommand(fix_code_1.COMMAND_FIX_CODE, async () => {
        await (0, fix_code_1.fixCode)(controller);
    }), vscode.commands.registerCommand(new_chat_conversation_1.COMMAND_NEW_CHAT_CONVERSATION, async () => {
        await (0, new_chat_conversation_1.newChatConversation)(controller);
    }), vscode.commands.registerCommand(constants_1.COMMAND_COPY_CODE_SNIPPET_FROM_QUICK_CHAT, async ({ code }) => {
        await vscode.env.clipboard.writeText(code);
        await vscode.window.showInformationMessage('Code copied to clipboard!');
    }), vscode.commands.registerCommand(constants_1.COMMAND_INSERT_CODE_SNIPPET_FROM_QUICK_CHAT, utils_1.insertQuickChatSnippetCommand), vscode.commands.registerCommand(close_gitlab_chat_1.COMMAND_CLOSE_GITLAB_CHAT, async () => {
        await (0, close_gitlab_chat_1.closeGitLabChat)(controller);
    }), vscode.commands.registerCommand(focus_gitlab_chat_1.COMMAND_FOCUS_GITLAB_CHAT, async () => {
        await (0, focus_gitlab_chat_1.focusGitLabChat)(controller);
    }));
};
exports.activateChat = activateChat;
//# sourceMappingURL=gitlab_chat.js.map