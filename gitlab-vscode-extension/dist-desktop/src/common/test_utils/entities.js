"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createExtensionContext = exports.gitlabPlatformForProject = exports.gitlabPlatformForAccount = exports.createFakeCable = exports.account = exports.project = exports.gqlProject = void 0;
const vscode_1 = __importDefault(require("vscode"));
const in_memory_memento_1 = require("../../../test/integration/test_infrastructure/in_memory_memento");
const get_project_1 = require("../gitlab/api/get_project");
const action_cable_1 = require("../gitlab/api/action_cable");
const create_fake_fetch_from_api_1 = require("./create_fake_fetch_from_api");
const secret_storage_1 = require("./secret_storage");
const create_fake_partial_1 = require("./create_fake_partial");
exports.gqlProject = {
    id: 'gid://gitlab/Project/5261717',
    name: 'gitlab-vscode-extension',
    description: '',
    fullPath: 'gitlab-org/gitlab-vscode-extension',
    webUrl: 'https://gitlab.com/gitlab-org/gitlab-vscode-extension',
    group: {
        id: 'gid://gitlab/Group/9970',
    },
};
exports.project = (0, get_project_1.convertToGitLabProject)(exports.gqlProject);
exports.account = {
    username: 'foobar',
    id: 'foobar',
    type: 'token',
    instanceUrl: 'http://gitlab-instance.xx',
    token: 'foobar-token',
};
const createFakeCable = () => (0, create_fake_partial_1.createFakePartial)({
    subscribe: jest.fn(),
    disconnect: jest.fn(),
});
exports.createFakeCable = createFakeCable;
exports.gitlabPlatformForAccount = {
    type: 'account',
    account: exports.account,
    project: undefined,
    fetchFromApi: (0, create_fake_fetch_from_api_1.createFakeFetchFromApi)(),
    connectToCable: async () => (0, exports.createFakeCable)(),
    getUserAgentHeader: () => ({}),
};
exports.gitlabPlatformForProject = {
    type: 'project',
    project: exports.project,
    account: exports.account,
    fetchFromApi: (0, create_fake_fetch_from_api_1.createFakeFetchFromApi)(),
    connectToCable: () => (0, action_cable_1.connectToCable)(''),
    getUserAgentHeader: () => ({}),
};
const createExtensionContext = () => (0, create_fake_partial_1.createFakePartial)({
    globalState: new in_memory_memento_1.InMemoryMemento(),
    workspaceState: new in_memory_memento_1.InMemoryMemento(),
    secrets: new secret_storage_1.SecretStorage(),
    extensionPath: '',
    subscriptions: [],
    extensionUri: vscode_1.default.Uri.parse('https://localhost'),
    asAbsolutePath: relativePath => `/test/abs/${relativePath}`,
});
exports.createExtensionContext = createExtensionContext;
//# sourceMappingURL=entities.js.map