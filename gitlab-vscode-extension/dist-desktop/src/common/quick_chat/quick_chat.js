"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuickChat = void 0;
const vscode_1 = __importStar(require("vscode"));
const uuid_1 = require("uuid");
const lodash_1 = require("lodash");
const gitlab_chat_api_1 = require("../chat/gitlab_chat_api");
const gitlab_chat_file_context_1 = require("../chat/gitlab_chat_file_context");
const constants_1 = require("../chat/constants");
const do_not_await_1 = require("../utils/do_not_await");
const local_feature_flag_service_1 = require("../feature_flags/local_feature_flag_service");
const utils_1 = require("./utils");
const constants_2 = require("./constants");
const fix_with_duo_quick_chat_action_provider_1 = require("./code_actions/fix_with_duo_quick_chat_action_provider");
const quick_chat_hint_1 = require("./quick_chat_hint");
const quick_chat_state_1 = require("./quick_chat_state");
class QuickChat {
    #api;
    #commentThreadService;
    #hint;
    #gutterIcon;
    #responseProcessor;
    #disposables = [];
    #stateMachine;
    constructor(api, commentThreadService, gutterIcon, responseProcessor) {
        this.#api = api;
        this.#commentThreadService = commentThreadService;
        this.#gutterIcon = gutterIcon;
        this.#hint = new quick_chat_hint_1.QuickChatHint();
        this.#responseProcessor = responseProcessor;
        const handleSelectionChangeDebounced = (0, lodash_1.debounce)((event) => {
            this.#commentThreadService.updateThreadSelection(event.textEditor.document.uri, event.textEditor);
            this.#hint?.updateHint(event);
        }, 100);
        this.#stateMachine = (0, quick_chat_state_1.createQuickChatMachine)(this.#commentThreadService, this.#gutterIcon, this.#api, this.handleSendMessage.bind(this));
        this.#stateMachine.start();
        (0, utils_1.subscribeToStateChanges)({
            actorTitle: 'QuickChatStateMachine',
            actor: this.#stateMachine,
            callback: undefined,
        });
        this.#disposables.push(this.#commentThreadService, this.#hint, this.#gutterIcon, 
        // Registers a completion item provider for quick actions (e.g. /tests)
        vscode_1.default.languages.registerCompletionItemProvider({ scheme: 'comment', pattern: '**' }, { provideCompletionItems: utils_1.provideCompletionItems }, '/'), vscode_1.default.languages.registerCodeActionsProvider({ scheme: 'file' }, // register for all languages
        new fix_with_duo_quick_chat_action_provider_1.FixWithDuoQuickChatActionProvider(), { providedCodeActionKinds: fix_with_duo_quick_chat_action_provider_1.FixWithDuoQuickChatActionProvider.providedCodeActionKinds }), vscode_1.default.window.onDidChangeTextEditorSelection(handleSelectionChangeDebounced), vscode_1.default.workspace.onDidChangeConfiguration(() => {
            this.#hint?.onConfigChange();
        }), vscode_1.default.window.onDidChangeTextEditorVisibleRanges(async (event) => {
            const { thread } = this.#stateMachine.getSnapshot().context;
            if (thread && event.textEditor.document.uri === thread.uri) {
                this.#gutterIcon.toggleGutterIcon(thread);
            }
            if (!this.#isCollapseStateSync(this.#stateMachine.getSnapshot().value.threadCollapsibleState, thread?.collapsibleState))
                this.#stateMachine.send({
                    type: thread?.collapsibleState === vscode_1.CommentThreadCollapsibleState.Expanded
                        ? 'expandChat'
                        : 'collapseChat',
                    origin: 'non-command',
                });
            await this.#createOrDestroyHint();
        }), vscode_1.default.window.onDidChangeActiveTextEditor(async (event) => {
            this.#stateMachine.send({
                type: 'setDocument',
                documentPath: event?.document.uri,
            });
            await this.#createOrDestroyHint();
        }), vscode_1.default.workspace.onDidChangeTextDocument(event => {
            const thread = this.#commentThreadService.getThread();
            if (event.document.uri !== thread?.uri)
                return;
            this.#commentThreadService.updateThreadRange(event.contentChanges[0]);
            this.#gutterIcon.toggleGutterIcon(thread);
        }), vscode_1.default.commands.registerCommand(constants_2.COMMAND_OPEN_QUICK_CHAT, async () => {
            this.triggerNewChat();
        }), vscode_1.default.commands.registerCommand(constants_2.COMMAND_SEND_QUICK_CHAT, (reply) => this.#stateMachine.send({ type: 'sendMessage', input: { reply } })), vscode_1.default.commands.registerCommand(constants_2.COMMAND_SEND_QUICK_CHAT_DUPLICATE, (reply) => this.#stateMachine.send({ type: 'sendMessage', input: { reply } })), vscode_1.default.commands.registerCommand(constants_2.COMMAND_OPEN_QUICK_CHAT_WITH_SHORTCUT, async () => {
            await this.triggerNewChat({ trigger: constants_2.QUICK_CHAT_OPEN_TRIGGER.SHORTCUT });
        }), vscode_1.default.commands.registerCommand(constants_2.COMMAND_SHOW_AND_SEND_QUICK_CHAT_WITH_CONTEXT, async (openOptions) => {
            await this.triggerNewChat(openOptions);
        }), vscode_1.default.commands.registerCommand(constants_2.COMMAND_CLOSE_QUICK_CHAT, async () => {
            this.#stateMachine.send({ type: 'collapseChat', origin: 'command' });
        }));
    }
    async #createOrDestroyHint() {
        const isExpandedAndActive = this.#stateMachine.getSnapshot().value.threadCollapsibleState ===
            quick_chat_state_1.QuickChatState.THREAD_EXPANDED &&
            this.#stateMachine.getSnapshot().value.documentState === 'inCurrentDocument';
        if (isExpandedAndActive) {
            this.#hint?.dispose();
            this.#hint = undefined;
        }
        else {
            this.#hint = this.#hint ?? new quick_chat_hint_1.QuickChatHint();
        }
        await vscode_1.default.commands.executeCommand('setContext', 'gitlab:quickChatOpen', isExpandedAndActive);
    }
    triggerNewChat(context) {
        const editor = vscode_1.default.window.activeTextEditor;
        if (!editor)
            return;
        this.#trackChatOpenTelemetry(context);
        this.#stateMachine.send({ type: 'triggerQuickChat', openOptions: context });
    }
    async #handleUserMessage(input) {
        const { reply, openOptions } = input;
        this.#responseProcessor.init();
        this.#commentThreadService.addUserComment(reply.text);
        // shows 'GitLab Duo Chat is finding an answer' loading message
        this.#commentThreadService.addLoaderComment();
        let fileContext;
        if (openOptions?.document && openOptions?.range) {
            fileContext = (0, gitlab_chat_file_context_1.getFileContext)(openOptions.document, openOptions.range);
        }
        else {
            fileContext = (0, gitlab_chat_file_context_1.getActiveFileContext)();
        }
        const subscriptionId = (0, uuid_1.v4)();
        await this.#commentThreadService.setLoadingContext(true); // disables the 'Send' button
        await this.#api.subscribeToUpdates(this.#subscriptionUpdateHandler.bind(this), subscriptionId);
        try {
            const result = await this.#submitQuestion(reply, subscriptionId, fileContext);
            if (result?.aiAction?.threadId) {
                this.#stateMachine.send({
                    type: 'updateAiActionThreadId',
                    aiActionThreadId: result?.aiAction?.threadId,
                });
            }
            return result;
        }
        catch (error) {
            this.#stateMachine.send({ type: 'errorOccurred', error });
            throw error;
        }
    }
    async handleSendMessage(input) {
        const { reply } = input;
        this.#trackMessageSentTelemetry({ message: reply.text });
        const isHandled = await this.#tryHandleSpecialMessage(reply.text);
        if (isHandled) {
            return;
        }
        await this.#handleUserMessage(input);
    }
    async #submitQuestion(reply, subId, fileContext) {
        await (0, utils_1.openAndShowDocument)(reply.thread.uri);
        return this.#api.processNewUserPrompt(reply.text, subId, fileContext, undefined, gitlab_chat_api_1.ConversationType.DUO_QUICK_CHAT, this.#stateMachine.getSnapshot().context.aiActionThreadId);
    }
    async #subscriptionUpdateHandler(data) {
        this.#stateMachine.send({ type: 'streamingResponse' });
        this.#commentThreadService.removeLoaderComment();
        this.#commentThreadService.addResponseComment(this.#responseProcessor.getResponse());
        this.#responseProcessor.processUpdate(data, () => {
            this.#stateMachine.send({ type: 'requestComplete' });
        });
        this.#commentThreadService.refreshComments();
        await this.#commentThreadService.setLoadingContext(false);
    }
    async #tryHandleSpecialMessage(text) {
        switch (text.trim().toLowerCase()) {
            case constants_1.SPECIAL_MESSAGES.CLEAR:
                this.#stateMachine.send({ type: 'executeSpecialCommand', command: constants_1.SPECIAL_MESSAGES.CLEAR });
                return true;
            case constants_1.SPECIAL_MESSAGES.RESET:
                this.#stateMachine.send({ type: 'executeSpecialCommand', command: constants_1.SPECIAL_MESSAGES.RESET });
                return true;
            default:
                // Not a special message
                return false;
        }
    }
    #trackChatOpenTelemetry(context) {
        if (!(0, local_feature_flag_service_1.getLocalFeatureFlagService)().isEnabled(local_feature_flag_service_1.FeatureFlag.LanguageServer))
            return;
        const trigger = context?.trigger || constants_2.QUICK_CHAT_OPEN_TRIGGER.CLICK;
        (0, do_not_await_1.doNotAwait)(vscode_1.default.commands.executeCommand(constants_2.COMMAND_QUICK_CHAT_OPEN_TELEMETRY, {
            trigger,
        }));
    }
    #trackMessageSentTelemetry(context) {
        if (!(0, local_feature_flag_service_1.getLocalFeatureFlagService)().isEnabled(local_feature_flag_service_1.FeatureFlag.LanguageServer))
            return;
        (0, do_not_await_1.doNotAwait)(vscode_1.default.commands.executeCommand(constants_2.COMMAND_QUICK_CHAT_MESSAGE_TELEMETRY, {
            message: context.message,
        }));
    }
    #isCollapseStateSync = (stateCollapsibleState, threadCollapsibleState) => {
        // If no thread state exists, check if the state is NO_QUICK_CHAT
        if (!threadCollapsibleState) {
            return stateCollapsibleState === quick_chat_state_1.QuickChatState.NO_QUICK_CHAT;
        }
        // Map thread collapsible states to corresponding quick chat states
        const stateMap = new Map([
            [vscode_1.default.CommentThreadCollapsibleState.Expanded, quick_chat_state_1.QuickChatState.THREAD_EXPANDED],
            [vscode_1.default.CommentThreadCollapsibleState.Collapsed, quick_chat_state_1.QuickChatState.THREAD_COLLAPSED],
        ]);
        // Check if the current state matches the expected state for the thread's collapsible state
        return stateCollapsibleState === stateMap.get(threadCollapsibleState);
    };
    dispose() {
        this.#disposables.forEach(disposable => disposable?.dispose());
    }
}
exports.QuickChat = QuickChat;
//# sourceMappingURL=quick_chat.js.map