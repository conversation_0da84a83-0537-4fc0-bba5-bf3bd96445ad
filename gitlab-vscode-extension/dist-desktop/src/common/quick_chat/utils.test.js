"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const vscode_1 = __importDefault(require("vscode"));
const create_fake_partial_1 = require("../test_utils/create_fake_partial");
const InsertCodeSnippetModule = __importStar(require("../chat/insert_code_snippet"));
const utils_1 = require("./utils");
const constants_1 = require("./constants");
jest.mock('../chat/insert_code_snippet');
describe('Quick Chat Utils', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });
    describe('openAndShowDocument', () => {
        it('opens and show sthe document', async () => {
            const uri = vscode_1.default.Uri.file('test/path/to/file.txt');
            const mockEditor = (0, create_fake_partial_1.createFakePartial)({ selection: undefined });
            vscode_1.default.workspace.openTextDocument.mockResolvedValue('mockDocument');
            vscode_1.default.window.showTextDocument.mockResolvedValue(mockEditor);
            await (0, utils_1.openAndShowDocument)(uri);
            expect(vscode_1.default.workspace.openTextDocument).toHaveBeenCalledWith(uri);
            expect(vscode_1.default.window.showTextDocument).toHaveBeenCalledWith('mockDocument');
        });
    });
    describe('generatePlaceholderPrompt', () => {
        it('returns correct prompt for empty range', () => {
            const emptyRange = new vscode_1.default.Range(0, 0, 0, 0);
            expect((0, utils_1.generateThreadLabel)(emptyRange)).toBe('Duo Quick Chat (select some code to add context)');
        });
        it('returns correct prompt for single line range', () => {
            const singleLineRange = new vscode_1.default.Range(5, 0, 5, 10);
            expect((0, utils_1.generateThreadLabel)(singleLineRange)).toBe('Duo Quick Chat (include line 6)');
        });
        it('returns correct prompt for multi-line range', () => {
            const multiLineRange = new vscode_1.default.Range(10, 0, 15, 5);
            expect((0, utils_1.generateThreadLabel)(multiLineRange)).toBe('Duo Quick Chat (include lines 11-16)');
        });
    });
    describe('provideCompletionItems', () => {
        const mockPosition = (0, create_fake_partial_1.createFakePartial)({ character: 1 });
        const mockDocument = (text) => (0, create_fake_partial_1.createFakePartial)({
            lineAt: jest.fn().mockReturnValue({ text }),
        });
        it('should return undefined if line does not start with "/"', () => {
            const result = (0, utils_1.provideCompletionItems)(mockDocument('some text'), mockPosition);
            expect(result).toBeUndefined();
        });
        it('should return completion items if line starts with "/"', () => {
            const result = (0, utils_1.provideCompletionItems)(mockDocument('/'), mockPosition);
            const kind = vscode_1.default.CompletionItemKind.Text;
            const expectedActions = [
                {
                    label: '/tests',
                    insertText: 'tests',
                    detail: 'Write tests for the selected snippet.',
                    kind,
                },
                {
                    label: '/refactor',
                    insertText: 'refactor',
                    detail: 'Refactor the selected snippet.',
                    kind,
                },
                {
                    label: '/explain',
                    insertText: 'explain',
                    detail: 'Explain the selected snippet.',
                    kind,
                },
                {
                    label: '/fix',
                    insertText: 'fix',
                    detail: 'Fix the selected code snippet.',
                    kind,
                },
                {
                    label: '/clear',
                    insertText: 'clear',
                    detail: 'Delete all messages in this conversation.',
                    kind,
                },
                {
                    label: '/reset',
                    insertText: 'reset',
                    detail: 'Reset conversation and ignore the previous messages.',
                    kind,
                },
            ];
            expect(result).toEqual(expectedActions);
        });
    });
    describe('addCopyAndInsertButtonsToCodeBlocks', () => {
        it(`adds copy and insert buttons to code blocks`, () => {
            const markdown = '```javascript\nconst x = 1;\n```';
            const result = (0, utils_1.addCopyAndInsertButtonsToCodeBlocks)(markdown);
            expect(result).toContain('```javascript\nconst x = 1;\n```');
            expect(result).toContain(`[**Copy Snippet**](command:${constants_1.COMMAND_COPY_CODE_SNIPPET_FROM_QUICK_CHAT}?`);
            expect(result).toContain(`[**Insert Snippet**](command:${constants_1.COMMAND_INSERT_CODE_SNIPPET_FROM_QUICK_CHAT}?`);
        });
        it('handles multiple code blocks', () => {
            const markdown = '```python\nprint("Hello")\n```\nSome text\n```ruby\nputs "World"\n```';
            const result = (0, utils_1.addCopyAndInsertButtonsToCodeBlocks)(markdown);
            expect(result).toContain('```python\nprint("Hello")\n```');
            expect(result).toContain('```ruby\nputs "World"\n```');
            expect(result.match(/\[\*\*Copy Snippet\*\*\]/g)).toHaveLength(2);
            expect(result.match(/\[\*\*Insert Snippet\*\*\]/g)).toHaveLength(2);
        });
        it('preserves non-code block content', () => {
            const markdown = 'Some text\n```css\nbody { color: red; }\n```\nMore text';
            const result = (0, utils_1.addCopyAndInsertButtonsToCodeBlocks)(markdown);
            expect(result).toContain('Some text');
            expect(result).toContain('```css\nbody { color: red; }\n```');
            expect(result).toContain('More text');
            expect(result).toContain(`[**Copy Snippet**](command:${constants_1.COMMAND_COPY_CODE_SNIPPET_FROM_QUICK_CHAT}?`);
            expect(result).toContain(`[**Insert Snippet**](command:${constants_1.COMMAND_INSERT_CODE_SNIPPET_FROM_QUICK_CHAT}?`);
        });
        it('handles code blocks without language specification', () => {
            const markdown = '```\nconst y = 2;\n```';
            const result = (0, utils_1.addCopyAndInsertButtonsToCodeBlocks)(markdown);
            expect(result).toContain('```\nconst y = 2;\n```');
            expect(result).toContain(`[**Copy Snippet**](command:${constants_1.COMMAND_COPY_CODE_SNIPPET_FROM_QUICK_CHAT}?`);
            expect(result).toContain(`[**Insert Snippet**](command:${constants_1.COMMAND_INSERT_CODE_SNIPPET_FROM_QUICK_CHAT}?`);
        });
        it('encodes command arguments correctly', () => {
            const markdown = '```json\n{"key": "value"}\n```';
            const result = (0, utils_1.addCopyAndInsertButtonsToCodeBlocks)(markdown);
            const encodedArgs = encodeURIComponent(JSON.stringify({ code: '{"key": "value"}' }));
            expect(result).toContain(`command:${constants_1.COMMAND_COPY_CODE_SNIPPET_FROM_QUICK_CHAT}?${encodedArgs}`);
            expect(result).toContain(`command:${constants_1.COMMAND_INSERT_CODE_SNIPPET_FROM_QUICK_CHAT}?${encodedArgs}`);
        });
    });
    describe('MarkdownProcessorPipeline', () => {
        let pipeline;
        beforeEach(() => {
            pipeline = new utils_1.MarkdownProcessorPipeline([]);
        });
        it('should process markdown through all processors', () => {
            const processor1 = jest.fn((md) => `${md} processed 1`);
            const processor2 = jest.fn((md) => `${md} processed 2`);
            pipeline.addProcessor(processor1);
            pipeline.addProcessor(processor2);
            const result = pipeline.process('test');
            expect(processor1).toHaveBeenCalledWith('test');
            expect(processor2).toHaveBeenCalledWith('test processed 1');
            expect(result).toBe('test processed 1 processed 2');
        });
        it('should return the original markdown if no processors are added', () => {
            const result = pipeline.process('test');
            expect(result).toBe('test');
        });
        it('should allow adding processors after initialization', () => {
            const processor = jest.fn((md) => `${md} processed 3`);
            pipeline.addProcessor(processor);
            const result = pipeline.process('test');
            expect(processor).toHaveBeenCalledWith('test');
            expect(result).toBe('test processed 3');
        });
    });
    describe('insertQuickChatSnippetCommand', () => {
        const mockEditor = (0, create_fake_partial_1.createFakePartial)({
            insertSnippet: jest.fn(),
        });
        const code = 'const x = 1;';
        beforeEach(() => {
            vscode_1.default.window.activeTextEditor = mockEditor;
        });
        it('inserts the code snippet when an active editor is present', async () => {
            await (0, utils_1.insertQuickChatSnippetCommand)({ code });
            expect(InsertCodeSnippetModule.insertCodeSnippet).toHaveBeenCalledWith(code);
        });
        it('does nothing when an invalid code is passed', async () => {
            await (0, utils_1.insertQuickChatSnippetCommand)({ code: '' });
            expect(mockEditor.insertSnippet).not.toHaveBeenCalled();
            expect(vscode_1.default.window.showWarningMessage).not.toHaveBeenCalled();
            expect(vscode_1.default.window.showInformationMessage).not.toHaveBeenCalled();
        });
    });
});
//# sourceMappingURL=utils.test.js.map