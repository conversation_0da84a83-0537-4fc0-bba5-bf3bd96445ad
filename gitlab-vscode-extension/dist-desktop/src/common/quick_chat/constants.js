"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QUICK_CHAT_OPEN_TRIGGER = exports.COMMAND_SHOW_AND_SEND_QUICK_CHAT_WITH_CONTEXT = exports.COMMAND_CLOSE_QUICK_CHAT = exports.COMMAND_INSERT_CODE_SNIPPET_FROM_QUICK_CHAT = exports.COMMAND_QUICK_CHAT_MESSAGE_TELEMETRY = exports.COMMAND_QUICK_CHAT_OPEN_TELEMETRY = exports.COMMAND_COPY_CODE_SNIPPET_FROM_QUICK_CHAT = exports.COMMAND_SEND_QUICK_CHAT_DUPLICATE = exports.COMMAND_SEND_QUICK_CHAT = exports.COMMAND_OPEN_QUICK_CHAT_WITH_SHORTCUT = exports.COMMAND_OPEN_QUICK_CHAT = void 0;
exports.COMMAND_OPEN_QUICK_CHAT = 'gl.openQuickChat';
exports.COMMAND_OPEN_QUICK_CHAT_WITH_SHORTCUT = 'gl.openQuickChatWithShortcut';
// we cannot update the command action label dynamically,
// instead we can register two identical commands that will execute
// based on the platform variable.
// That's why we have 2 commands to send quick chat
exports.COMMAND_SEND_QUICK_CHAT = 'gl.sendQuickChat';
exports.COMMAND_SEND_QUICK_CHAT_DUPLICATE = 'gl.sendQuickChatDup';
exports.COMMAND_COPY_CODE_SNIPPET_FROM_QUICK_CHAT = 'gl.copyCodeSnippetFromQuickChat';
exports.COMMAND_QUICK_CHAT_OPEN_TELEMETRY = 'gl.quickChatOpenTelemetry';
exports.COMMAND_QUICK_CHAT_MESSAGE_TELEMETRY = 'gl.quickChatMessageSentTelemetry';
exports.COMMAND_INSERT_CODE_SNIPPET_FROM_QUICK_CHAT = 'gl.insertCodeSnippetFromQuickChat';
exports.COMMAND_CLOSE_QUICK_CHAT = 'gl.closeQuickChat';
exports.COMMAND_SHOW_AND_SEND_QUICK_CHAT_WITH_CONTEXT = 'gl.showAndSendDuoQuickChatWithContext';
// TODO: when LS is enabled in IDE, we can replace this with LS import
var QUICK_CHAT_OPEN_TRIGGER;
(function (QUICK_CHAT_OPEN_TRIGGER) {
    QUICK_CHAT_OPEN_TRIGGER["SHORTCUT"] = "shortcut";
    QUICK_CHAT_OPEN_TRIGGER["CLICK"] = "btn_click";
    QUICK_CHAT_OPEN_TRIGGER["CODE_ACTION_FIX_WITH_DUO"] = "code_action_fix_with_duo";
})(QUICK_CHAT_OPEN_TRIGGER || (exports.QUICK_CHAT_OPEN_TRIGGER = QUICK_CHAT_OPEN_TRIGGER = {}));
//# sourceMappingURL=constants.js.map