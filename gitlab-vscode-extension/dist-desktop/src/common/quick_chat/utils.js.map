{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../../src/common/quick_chat/utils.ts"], "names": [], "mappings": ";;;;;;AAsHA,0DA8CC;AApKD,oDAA4B;AAE5B,qEAAgE;AAChE,gCAA6B;AAC7B,2CAGqB;AAER,QAAA,qBAAqB,GAAG,gBAAgB,CAAC;AAE/C,MAAM,mBAAmB,GAAG,KAAK,EAAE,GAAe,EAAE,EAAE,CAC3D,gBAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,gBAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;AADlE,QAAA,mBAAmB,uBAC+C;AAExE,MAAM,mBAAmB,GAAG,CAAC,KAAmB,EAAE,EAAE;IACzD,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;IACvC,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;IACnC,MAAM,MAAM,GAAG,gBAAgB,CAAC;IAEhC,IAAI,KAAK,CAAC,OAAO;QAAE,OAAO,GAAG,MAAM,oCAAoC,CAAC;IACxE,IAAI,SAAS,KAAK,OAAO;QAAE,OAAO,GAAG,MAAM,kBAAkB,SAAS,GAAG,CAAC;IAC1E,OAAO,GAAG,MAAM,mBAAmB,SAAS,IAAI,OAAO,GAAG,CAAC;AAC7D,CAAC,CAAC;AARW,QAAA,mBAAmB,uBAQ9B;AAEK,MAAM,sBAAsB,GAAG,CACpC,QAA6B,EAC7B,QAAyB,EACzB,EAAE;IACF,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;IACnF,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,GAAG;QAAE,OAAO,SAAS,CAAC;IAEhD,MAAM,OAAO,GAAG;QACd,CAAC,OAAO,EAAE,uCAAuC,CAAC;QAClD,CAAC,UAAU,EAAE,gCAAgC,CAAC;QAC9C,CAAC,SAAS,EAAE,+BAA+B,CAAC;QAC5C,CAAC,KAAK,EAAE,gCAAgC,CAAC;QACzC,CAAC,OAAO,EAAE,2CAA2C,CAAC;QACtD,CAAC,OAAO,EAAE,sDAAsD,CAAC;KAClE,CAAC;IAEF,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,EAAE;QACrC,MAAM,IAAI,GAAG,IAAI,gBAAM,CAAC,cAAc,CAAC,IAAI,KAAK,EAAE,EAAE,gBAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACpF,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAtBW,QAAA,sBAAsB,0BAsBjC;AAIF;;;;GAIG;AACH,MAAa,yBAAyB;IACpC,WAAW,CAAsB;IAEjC,YAAY,UAA+B;QACzC,IAAI,CAAC,WAAW,GAAG,UAAU,IAAI,EAAE,CAAC;IACtC,CAAC;IAED,YAAY,CAAC,SAAuC;QAClD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;IAED,OAAO,CAAC,QAAgB;QACtB,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,CAAC;IACrF,CAAC;CACF;AAdD,8DAcC;AAED;;GAEG;AACI,MAAM,mCAAmC,GAAsB,QAAQ,CAAC,EAAE;IAC/E,yFAAyF;IACzF,MAAM,cAAc,GAAG,8CAA8C,CAAC;IAEtE,4DAA4D;IAC5D,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CACtC,cAAc,EACd,CAAC,YAAoB,EAAE,QAA4B,EAAE,WAAmB,EAAE,EAAE;QAC1E,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,MAAM,kBAAkB,GAAG,QAAQ,IAAI,EAAE,CAAC;QAE1C,MAAM,WAAW,GAAG;YAClB,IAAI,EAAE,WAAW;SAClB,CAAC;QAEF,MAAM,QAAQ,GAAG,8BAA8B,qDAAyC,IAAI,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,sCAAsC,CAAC;QAClL,MAAM,UAAU,GAAG,gCAAgC,uDAA2C,IAAI,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,wCAAwC,CAAC;QAE1L,4EAA4E;QAC5E,OAAO,KAAK,QAAQ,MAAM,UAAU,WAAW,kBAAkB,KAAK,WAAW,UAAU,CAAC;IAC9F,CAAC,CACF,CAAC;IAEF,OAAO,eAAe,CAAC;AACzB,CAAC,CAAC;AA3BW,QAAA,mCAAmC,uCA2B9C;AAEK,MAAM,6BAA6B,GAAyC,KAAK,EAAE,EACxF,IAAI,GACL,EAAE,EAAE;IACH,IAAI,IAAI,EAAE,CAAC;QACT,MAAM,IAAA,uCAAiB,EAAC,IAAI,CAAC,CAAC;IAChC,CAAC;AACH,CAAC,CAAC;AANW,QAAA,6BAA6B,iCAMxC;AAEF;;;;;;GAMG;AACH,SAAgB,uBAAuB,CAAC,EACtC,UAAU,EACV,KAAK,EACL,QAAQ,GAKT;IACC,IAAI,aAAa,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC;IAE9C,MAAM,YAAY,GAAG,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;QAC9C,IAAI,aAAa,KAAK,QAAQ,CAAC,KAAK,EAAE,CAAC;YACrC,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE,CAAC;gBACtC,SAAG,CAAC,KAAK,CAAC,IAAI,UAAU,0BAA0B,aAAa,SAAS,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC;YAC7F,CAAC;iBAAM,CAAC;gBACN,uEAAuE;gBACvE,MAAM,gBAAgB,GAAa,EAAE,CAAC;gBACtC,MAAM,OAAO,GAAG;oBACd,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;iBAC5E,CAAC;gBACF,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBACpB,MAAM,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;oBACrC,MAAM,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBAEzC,oCAAoC;oBACpC,IAAI,SAAS,KAAK,YAAY,EAAE,CAAC;wBAC/B,gBAAgB,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,SAAS,SAAS,YAAY,GAAG,CAAC,CAAC;oBACzE,CAAC;gBACH,CAAC,CAAC,CAAC;gBACH,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAChC,MAAM,gBAAgB,GAAG,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACtD,SAAG,CAAC,KAAK,CAAC,IAAI,UAAU,qBAAqB,gBAAgB,EAAE,CAAC,CAAC;gBACnE,CAAC;YACH,CAAC;YAED,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC;YAE/B,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC;QACvB,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,mCAAmC;IACnC,OAAO,GAAG,EAAE;QACV,YAAY,CAAC,WAAW,EAAE,CAAC;IAC7B,CAAC,CAAC;AACJ,CAAC"}