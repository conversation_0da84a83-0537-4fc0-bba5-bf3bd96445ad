"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.insertQuickChatSnippetCommand = exports.addCopyAndInsertButtonsToCodeBlocks = exports.MarkdownProcessorPipeline = exports.provideCompletionItems = exports.generateThreadLabel = exports.openAndShowDocument = exports.COMMENT_CONTROLLER_ID = void 0;
exports.subscribeToStateChanges = subscribeToStateChanges;
const vscode_1 = __importDefault(require("vscode"));
const insert_code_snippet_1 = require("../chat/insert_code_snippet");
const log_1 = require("../log");
const constants_1 = require("./constants");
exports.COMMENT_CONTROLLER_ID = 'duo-quick-chat';
const openAndShowDocument = async (uri) => vscode_1.default.window.showTextDocument(await vscode_1.default.workspace.openTextDocument(uri));
exports.openAndShowDocument = openAndShowDocument;
const generateThreadLabel = (range) => {
    const startLine = range.start.line + 1;
    const endLine = range.end.line + 1;
    const prefix = 'Duo Quick Chat';
    if (range.isEmpty)
        return `${prefix} (select some code to add context)`;
    if (startLine === endLine)
        return `${prefix} (include line ${startLine})`;
    return `${prefix} (include lines ${startLine}-${endLine})`;
};
exports.generateThreadLabel = generateThreadLabel;
const provideCompletionItems = (document, position) => {
    const linePrefix = document.lineAt(position).text.substring(0, position.character);
    if (linePrefix.trim() !== '/')
        return undefined;
    const actions = [
        ['tests', 'Write tests for the selected snippet.'],
        ['refactor', 'Refactor the selected snippet.'],
        ['explain', 'Explain the selected snippet.'],
        ['fix', 'Fix the selected code snippet.'],
        ['clear', 'Delete all messages in this conversation.'],
        ['reset', 'Reset conversation and ignore the previous messages.'],
    ];
    return actions.map(([label, detail]) => {
        const item = new vscode_1.default.CompletionItem(`/${label}`, vscode_1.default.CompletionItemKind.Text);
        item.detail = detail;
        item.insertText = label;
        return item;
    });
};
exports.provideCompletionItems = provideCompletionItems;
/**
 * class that wil hold a pipeline of markdown processors.
 * Each processor will be a function that takes a markdown string
 * and returns a modified markdown string.
 */
class MarkdownProcessorPipeline {
    #processors;
    constructor(processors) {
        this.#processors = processors ?? [];
    }
    addProcessor(processor) {
        this.#processors.push(processor);
    }
    process(markdown) {
        return this.#processors.reduce((result, processor) => processor(result), markdown);
    }
}
exports.MarkdownProcessorPipeline = MarkdownProcessorPipeline;
/**
 * Function to parse the markdown and add "Copy" buttons to code blocks.
 */
const addCopyAndInsertButtonsToCodeBlocks = markdown => {
    // Regular expression to match code blocks in markdown (` ```language \n code... \n``` `)
    const codeBlockRegex = /^\s*```(\w+)?\s*$\n([\s\S]*?)\n^\s*```\s*$/gm;
    // Replace each code block with an appended copy button link
    const updatedMarkdown = markdown.replace(codeBlockRegex, (originalCode, language, codeContent) => {
        if (!codeContent) {
            return originalCode;
        }
        const languageIdentifier = language ?? '';
        const commandArgs = {
            code: codeContent,
        };
        const copyLink = `[**Copy Snippet**](command:${constants_1.COMMAND_COPY_CODE_SNIPPET_FROM_QUICK_CHAT}?${encodeURIComponent(JSON.stringify(commandArgs))} "Click to copy this code snippet!")`;
        const insertLink = `[**Insert Snippet**](command:${constants_1.COMMAND_INSERT_CODE_SNIPPET_FROM_QUICK_CHAT}?${encodeURIComponent(JSON.stringify(commandArgs))} "Click to insert this code snippet!")`;
        // Reconstruct the original code block and prepend the copy and insert links
        return `\n${copyLink} | ${insertLink}\n\`\`\`${languageIdentifier}\n${codeContent}\n\`\`\``;
    });
    return updatedMarkdown;
};
exports.addCopyAndInsertButtonsToCodeBlocks = addCopyAndInsertButtonsToCodeBlocks;
const insertQuickChatSnippetCommand = async ({ code, }) => {
    if (code) {
        await (0, insert_code_snippet_1.insertCodeSnippet)(code);
    }
};
exports.insertQuickChatSnippetCommand = insertQuickChatSnippetCommand;
/**
 * Creates a subscription to the state machine that only triggers when the state value changes
 * @param actorTitle name of the actor for logging
 * @param actor xState actor to subscribe to
 * @param callback optional callback to execute when state changes
 * @returns A function to unsubscribe
 */
function subscribeToStateChanges({ actorTitle, actor, callback, }) {
    let previousState = actor.getSnapshot().value;
    const subscription = actor.subscribe(snapshot => {
        if (previousState !== snapshot.value) {
            if (typeof previousState === 'string') {
                log_1.log.debug(`[${actorTitle}]: state changed from "${previousState}" to "${snapshot.value}"`);
            }
            else {
                // if actor is a parallel state machine, state is returned as an object
                const stateChangeLines = [];
                const allKeys = [
                    ...new Set([...Object.keys(previousState), ...Object.keys(snapshot.value)]),
                ];
                allKeys.forEach(key => {
                    const prevValue = previousState[key];
                    const currentValue = snapshot.value[key];
                    // Only show properties that changed
                    if (prevValue !== currentValue) {
                        stateChangeLines.push(`[${key}]: "${prevValue}" to "${currentValue}"`);
                    }
                });
                if (stateChangeLines.length > 0) {
                    const formattedChanges = stateChangeLines.join(',\n');
                    log_1.log.debug(`[${actorTitle}]: State changes: ${formattedChanges}`);
                }
            }
            previousState = snapshot.value;
            callback?.(snapshot);
        }
    });
    // return a function to unsubscribe
    return () => {
        subscription.unsubscribe();
    };
}
//# sourceMappingURL=utils.js.map