"use strict";
/*
 * ------------------------------------
 * This file contains types WebIDE shares with this (and other) projects.
 * If you change this file, you MUST change it also in:
 *   - https://gitlab.com/gitlab-org/gitlab-web-ide/-/blob/main/packages/web-ide-interop/src/index.ts
 * ------------------------------------
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.COMMAND_GET_CONFIG = exports.COMMAND_MEDIATOR_TOKEN = exports.COMMAND_FETCH_BUFFER_FROM_API = exports.COMMAND_FETCH_FROM_API = exports.WEB_IDE_AUTH_SCOPE = exports.WEB_IDE_AUTH_PROVIDER_ID = exports.WEB_IDE_EXTENSION_ID = void 0;
// why: `TReturnType` helps encapsulate the full request type when used with `fetchFromApi`
/* eslint @typescript-eslint/no-unused-vars: ["error", { "varsIgnorePattern": "TReturnType" }] */
// region: Shared constants --------------------------------------------
exports.WEB_IDE_EXTENSION_ID = 'gitlab.gitlab-web-ide';
exports.WEB_IDE_AUTH_PROVIDER_ID = 'gitlab-web-ide';
exports.WEB_IDE_AUTH_SCOPE = 'api';
// region: Mediator commands -------------------------------------------
exports.COMMAND_FETCH_FROM_API = `gitlab-web-ide.mediator.fetch-from-api`;
exports.COMMAND_FETCH_BUFFER_FROM_API = `gitlab-web-ide.mediator.fetch-buffer-from-api`;
exports.COMMAND_MEDIATOR_TOKEN = `gitlab-web-ide.mediator.mediator-token`;
exports.COMMAND_GET_CONFIG = `gitlab-web-ide.mediator.get-config`;
//# sourceMappingURL=web_ide.js.map