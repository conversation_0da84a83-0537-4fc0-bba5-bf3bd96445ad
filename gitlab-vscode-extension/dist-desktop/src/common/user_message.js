"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserMessage = void 0;
const vscode = __importStar(require("vscode"));
class UserMessage {
    #storageKey;
    #message;
    #globalState;
    #actions;
    // we only show the message once per VS Code window even if the user only dismisses the message
    #hasBeenShownInSession = false;
    /**
     * @param storageKey - the string should be date prefixed to ensure we avoid conflicts in the local storage e.g. `2025-01-15-message-singleAccount`
     */
    constructor(globalState, storageKey, message, actions = []) {
        this.#globalState = globalState;
        this.#storageKey = storageKey;
        this.#message = message;
        this.#actions = actions;
    }
    async trigger() {
        if (this.#globalState.get(this.#storageKey))
            return;
        if (this.#hasBeenShownInSession)
            return;
        this.#hasBeenShownInSession = true;
        const actionTitles = this.#actions.map(a => a.title);
        const allOptions = [...actionTitles, "Don't show again"];
        const selection = await vscode.window.showInformationMessage(this.#message, ...allOptions);
        if (selection === "Don't show again") {
            await this.#globalState.update(this.#storageKey, true);
            return;
        }
        const selectedAction = this.#actions.find(a => a.title === selection);
        if (selectedAction) {
            await selectedAction.callback();
        }
    }
}
exports.UserMessage = UserMessage;
//# sourceMappingURL=user_message.js.map