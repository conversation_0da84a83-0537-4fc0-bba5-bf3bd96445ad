"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupVersionCheck = exports.validateGitLabVersion = exports.instanceUrlsWithShownWarnings = exports.versionRequest = void 0;
const vscode = __importStar(require("vscode"));
const constants_1 = require("../constants");
const log_1 = require("../log");
const if_version_gte_1 = require("../utils/if_version_gte");
exports.versionRequest = {
    type: 'rest',
    method: 'GET',
    path: '/version',
};
exports.instanceUrlsWithShownWarnings = {};
const DO_NOT_SHOW_AGAIN_TEXT = 'Do not show again';
const validateGitLabVersion = async (fetcher) => {
    const { version } = await fetcher.fetchFromApi(exports.versionRequest);
    return (0, if_version_gte_1.ifVersionGte)(version, constants_1.MINIMUM_VERSION, () => ({ valid: true }), () => ({ valid: false, current: version, minimum: constants_1.MINIMUM_VERSION }));
};
exports.validateGitLabVersion = validateGitLabVersion;
// FIXME: Custom messages like these are deprecated in favour of the
// src/common/user_message.ts component
const checkVersion = async (platform, context) => {
    const { instanceUrl } = platform.account;
    if (instanceUrl in exports.instanceUrlsWithShownWarnings)
        return;
    const validationResult = await (0, exports.validateGitLabVersion)(platform);
    if (!validationResult.valid) {
        const warningMessage = `
        This extension requires GitLab version ${validationResult.minimum} or later, but ${instanceUrl} is using ${validationResult.current}.
      `;
        log_1.log.warn(warningMessage);
        const versionWarningRecords = context.globalState.get(constants_1.DO_NOT_SHOW_VERSION_WARNING);
        if (versionWarningRecords?.[instanceUrl])
            return;
        exports.instanceUrlsWithShownWarnings[instanceUrl] = true;
        const action = await vscode.window.showErrorMessage(warningMessage, DO_NOT_SHOW_AGAIN_TEXT);
        if (action === DO_NOT_SHOW_AGAIN_TEXT)
            await context.workspaceState.update(constants_1.DO_NOT_SHOW_VERSION_WARNING, {
                ...versionWarningRecords,
                [instanceUrl]: true,
            });
    }
};
const setupVersionCheck = (platformManager, context) => {
    const subscriptions = [];
    subscriptions.push(platformManager.onAccountChange(async () => {
        const platform = await platformManager.getForActiveAccount(false);
        if (!platform)
            return;
        await checkVersion(platform, context);
    }));
    return {
        dispose: () => subscriptions.forEach(s => s.dispose()),
    };
};
exports.setupVersionCheck = setupVersionCheck;
//# sourceMappingURL=check_version.js.map