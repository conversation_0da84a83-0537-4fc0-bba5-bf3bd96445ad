"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleError = void 0;
const vscode = __importStar(require("vscode"));
const log_1 = require("../log");
const command_names_1 = require("../command_names");
const ui_error_1 = require("./ui_error");
const handleError = (e) => {
    log_1.log.error(e);
    if ((0, ui_error_1.isUiError)(e)) {
        e.showUi().catch(log_1.log.error);
        return;
    }
    const showErrorMessage = async () => {
        const choice = await vscode.window.showErrorMessage(e.message, 'Show Logs');
        if (choice === 'Show Logs') {
            await vscode.commands.executeCommand(command_names_1.USER_COMMANDS.SHOW_LOGS);
        }
    };
    // This is probably the only place where we want to ignore a floating promise.
    // We don't want to block the app and wait for user click on the "Show Logs"
    // button or close the message However, for testing this method, we need to
    // keep the promise.
    showErrorMessage().catch(log_1.log.error);
};
exports.handleError = handleError;
//# sourceMappingURL=handle_error.js.map