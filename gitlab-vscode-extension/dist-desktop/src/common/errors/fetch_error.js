"use strict";
/* eslint max-classes-per-file: 0 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimeoutError = exports.isMissingDefaultDuoGroupError = exports.FetchError = void 0;
const constants_1 = require("../constants");
const extract_url_1 = require("../utils/extract_url");
const common_1 = require("./common");
const getErrorType = (body) => {
    try {
        const parsedBody = JSON.parse(body);
        return parsedBody?.error;
    }
    catch {
        return undefined;
    }
};
const isInvalidTokenError = (response, body) => Boolean(response.status === 401 && body && getErrorType(body) === 'invalid_token');
const isInvalidRefresh = (response, body) => Boolean(response.status === 400 && body && getErrorType(body) === 'invalid_grant');
class FetchError extends Error {
    response;
    #body;
    constructor(response, resourceName, body) {
        let message = `Fetching ${resourceName} from ${response.url} failed`;
        if (isInvalidTokenError(response, body)) {
            message = `Request for ${resourceName} failed because the token is expired or revoked.`;
        }
        if (isInvalidRefresh(response, body)) {
            message = `Request to refresh token failed, because it's revoked or already refreshed.`;
        }
        super(message);
        this.response = response;
        this.#body = body;
    }
    get status() {
        return this.response.status;
    }
    isInvalidToken() {
        return (isInvalidTokenError(this.response, this.#body) || isInvalidRefresh(this.response, this.#body));
    }
    get details() {
        const { message, stack } = this;
        return {
            message,
            stack: (0, common_1.stackToArray)(stack),
            response: {
                status: this.response.status,
                headers: this.response.headers,
                body: this.#body,
            },
        };
    }
}
exports.FetchError = FetchError;
const isMissingDefaultDuoGroupError = (error) => {
    const isFetchError = error instanceof FetchError;
    if (!isFetchError)
        return false;
    const errorBody = error.details.response.body;
    if (!errorBody)
        return false;
    try {
        const body = JSON.parse(errorBody);
        return body.error === 'missing_default_duo_group';
    }
    catch {
        return false;
    }
};
exports.isMissingDefaultDuoGroupError = isMissingDefaultDuoGroupError;
class TimeoutError extends Error {
    constructor(url) {
        const timeoutInSeconds = Math.round(constants_1.REQUEST_TIMEOUT_MILLISECONDS / 1000);
        super(`Request to ${(0, extract_url_1.extractURL)(url)} timed out after ${timeoutInSeconds} second${timeoutInSeconds === 1 ? '' : 's'}`);
    }
}
exports.TimeoutError = TimeoutError;
//# sourceMappingURL=fetch_error.js.map