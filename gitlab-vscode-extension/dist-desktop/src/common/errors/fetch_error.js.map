{"version": 3, "file": "fetch_error.js", "sourceRoot": "", "sources": ["../../../../src/common/errors/fetch_error.ts"], "names": [], "mappings": ";AAAA,oCAAoC;;;AAGpC,4CAA4D;AAC5D,sDAAkD;AAClD,qCAAuD;AAEvD,MAAM,YAAY,GAAG,CAAC,IAAY,EAAoB,EAAE;IACtD,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACpC,OAAO,UAAU,EAAE,KAAK,CAAC;IAC3B,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC,CAAC;AAEF,MAAM,mBAAmB,GAAG,CAAC,QAAkB,EAAE,IAAa,EAAE,EAAE,CAChE,OAAO,CAAC,QAAQ,CAAC,MAAM,KAAK,GAAG,IAAI,IAAI,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,eAAe,CAAC,CAAC;AAErF,MAAM,gBAAgB,GAAG,CAAC,QAAkB,EAAE,IAAa,EAAE,EAAE,CAC7D,OAAO,CAAC,QAAQ,CAAC,MAAM,KAAK,GAAG,IAAI,IAAI,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,eAAe,CAAC,CAAC;AAErF,MAAa,UAAW,SAAQ,KAAK;IACnC,QAAQ,CAAW;IAEnB,KAAK,CAAU;IAEf,YAAY,QAAkB,EAAE,YAAoB,EAAE,IAAa;QACjE,IAAI,OAAO,GAAG,YAAY,YAAY,SAAS,QAAQ,CAAC,GAAG,SAAS,CAAC;QACrE,IAAI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;YACxC,OAAO,GAAG,eAAe,YAAY,kDAAkD,CAAC;QAC1F,CAAC;QACD,IAAI,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;YACrC,OAAO,GAAG,6EAA6E,CAAC;QAC1F,CAAC;QACD,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACpB,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;IAC9B,CAAC;IAED,cAAc;QACZ,OAAO,CACL,mBAAmB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,gBAAgB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAC9F,CAAC;IACJ,CAAC;IAED,IAAI,OAAO;QACT,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;QAChC,OAAO;YACL,OAAO;YACP,KAAK,EAAE,IAAA,qBAAY,EAAC,KAAK,CAAC;YAC1B,QAAQ,EAAE;gBACR,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;gBAC5B,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO;gBAC9B,IAAI,EAAE,IAAI,CAAC,KAAK;aACjB;SACF,CAAC;IACJ,CAAC;CACF;AAxCD,gCAwCC;AAEM,MAAM,6BAA6B,GAAG,CAAC,KAAc,EAAE,EAAE;IAC9D,MAAM,YAAY,GAAG,KAAK,YAAY,UAAU,CAAC;IAEjD,IAAI,CAAC,YAAY;QAAE,OAAO,KAAK,CAAC;IAEhC,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;IAC9C,IAAI,CAAC,SAAS;QAAE,OAAO,KAAK,CAAC;IAE7B,IAAI,CAAC;QACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAEnC,OAAO,IAAI,CAAC,KAAK,KAAK,2BAA2B,CAAC;IACpD,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC,CAAC;AAfW,QAAA,6BAA6B,iCAexC;AAEF,MAAa,YAAa,SAAQ,KAAK;IACrC,YAAY,GAAsB;QAChC,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,wCAA4B,GAAG,IAAI,CAAC,CAAC;QACzE,KAAK,CACH,cAAc,IAAA,wBAAU,EAAC,GAAG,CAAC,oBAAoB,gBAAgB,UAAU,gBAAgB,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAC/G,CAAC;IACJ,CAAC;CACF;AAPD,oCAOC"}