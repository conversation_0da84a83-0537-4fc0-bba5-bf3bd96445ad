"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const packageJson = __importStar(require("../../package.json"));
const build_supported_languages_1 = require("./test_utils/build_supported_languages");
describe('Supported Languages package.json config contribution', () => {
    it('contains all languages', () => {
        const supportedLangConfig = packageJson.contributes.configuration.find(c => c.id === 'duo')
            ?.properties[build_supported_languages_1.SUPPORTED_LANGUAGES_CONFIG_NAME];
        const { properties, defaultValues } = (0, build_supported_languages_1.buildSupportedLanguages)();
        try {
            expect(supportedLangConfig?.properties).toEqual(properties);
            expect(supportedLangConfig?.default).toEqual(defaultValues);
        }
        catch (e) {
            throw new Error(`The enabled supported languages configuration schema in package.json is out of date. You probably updated the \`@gitlab-org/gitlab-lsp\` dependency. Please run \`npm run update-supported-languages\`, to update the schema.\n\nFor more information see https://gitlab.com/gitlab-org/gitlab-vscode-extension/blob/main/docs/developer/code-suggestions-supported-languages.md\n\nThe exact error explaining what property is out of sync${e.message}`);
        }
    });
});
//# sourceMappingURL=supported_languages_package_json.test.js.map