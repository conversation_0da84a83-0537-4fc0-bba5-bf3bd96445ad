{"version": 3, "file": "log.js", "sourceRoot": "", "sources": ["../../../src/common/log.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAC1B,4CAAkD;AAClD,uCAA0C;AAC1C,6FAAwF;AAGxF,sCAAsC;AACtC,IAAI,SAAS,GAAgB,OAAO,CAAC,KAAK,CAAC;AAEpC,MAAM,iBAAiB,GAAG,CAAC,OAAoB,EAAQ,EAAE;IAC9D,SAAS,GAAG,OAAO,CAAC;AACtB,CAAC,CAAC;AAFW,QAAA,iBAAiB,qBAE5B;AAaF,MAAM,SAAS,GAAG;IAChB,KAAK,EAAE,OAAO;IACd,IAAI,EAAE,MAAM;IACZ,OAAO,EAAE,SAAS;IAClB,KAAK,EAAE,OAAO;CACN,CAAC;AAEX,mCAAmC;AACnC,MAAM,OAAO,GAAG,CAAC,CAAC;AAIlB,MAAM,YAAY,GAAG,CAAC,IAAY,EAAE,KAAe,EAAQ,EAAE;IAC3D,MAAM,MAAM,GAAG,GAAG,IAAA,eAAK,GAAE,CAAC,MAAM,CAAC,yBAAyB,CAAC,KAAK,KAAK,KAAK,CAAC;IAC3E,MAAM,YAAY,GAAG,CAAC,IAAY,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAEvF,SAAS,CAAC,GAAG,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC9C,CAAC,CAAC;AAEF,MAAM,WAAW,GAAG,CAAC,CAAQ,EAAU,EAAE,CACvC,IAAA,wBAAe,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAA,iBAAU,EAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC;AAE1E,MAAM,YAAY,GAAG,CAAC,KAAe,EAAE,EAAkB,EAAE,EAAU,EAAE,EAAE;IACvE,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE,CAAC;QAC3B,MAAM,SAAS,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACnD,YAAY,CAAC,GAAG,EAAE,GAAG,SAAS,EAAE,EAAE,KAAK,CAAC,CAAC;IAC3C,CAAC;SAAM,CAAC;QACN,YAAY,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACvC,CAAC;AACH,CAAC,CAAC;AAEF,mFAAmF;AACnF,MAAM,KAAK,GAAG,CAAC,EAAkB,EAAE,EAAU,EAAE,EAAE;IAC/C,MAAM,MAAM,GAAG,+DAA6B,CAAC,gBAAgB,EAAE,CAAC;IAChE,IAAI,MAAM,CAAC,KAAK;QAAE,YAAY,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAC1D,CAAC,CAAC;AACF,MAAM,IAAI,GAAG,CAAC,EAAkB,EAAE,EAAU,EAAE,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACtF,MAAM,IAAI,GAAG,CAAC,EAAkB,EAAE,EAAU,EAAE,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACzF,MAAM,KAAK,GAAG,CAAC,EAAkB,EAAE,EAAU,EAAE,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAE3E,QAAA,GAAG,GAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC"}