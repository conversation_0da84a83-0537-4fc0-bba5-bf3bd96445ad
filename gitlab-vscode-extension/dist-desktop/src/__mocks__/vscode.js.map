{"version": 3, "file": "vscode.js", "sourceRoot": "", "sources": ["../../../src/__mocks__/vscode.js"], "names": [], "mappings": ";AAAA,MAAM,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,2BAA2B,CAAC,CAAC;AACrD,MAAM,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC,qCAAqC,CAAC,CAAC;AACxE,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAChE,MAAM,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC,yCAAyC,CAAC,CAAC;AAC/E,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,+BAA+B,CAAC,CAAC;AAC9D,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAChE,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC,4BAA4B,CAAC,CAAC;AACxD,MAAM,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC,qCAAqC,CAAC,CAAC;AAExE,MAAM,iBAAiB,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAElF,MAAM,UAAU;IACd,YAAY,QAAQ;QAClB,IAAI,CAAC,OAAO,GAAG,OAAO,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC;IACtE,CAAC;IAED,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK;QAClB,OAAO,IAAI,UAAU,CAAC,GAAG,EAAE;YACzB,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAED,MAAM,CAAC,OAAO,GAAG;IACf,UAAU;IACV,QAAQ,EAAE,SAAS,QAAQ,CAAC,UAAU,EAAE,gBAAgB;QACtD,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;YACnC,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;QAC1B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAChC,CAAC;IACH,CAAC;IACD,SAAS,EAAE,SAAS,SAAS,CAAC,EAAE;QAC9B,OAAO,EAAE,EAAE,EAAE,CAAC;IAChB,CAAC;IACD,YAAY;IACZ,wBAAwB,EAAE;QACxB,SAAS,EAAE,WAAW;KACvB;IACD,cAAc,EAAE,SAAS,cAAc,CAAC,KAAK,EAAE,iBAAiB;QAC9D,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;IAC7C,CAAC;IACD,GAAG;IACH,YAAY;IACZ,cAAc,EAAE;QACd,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;QACrB,mBAAmB,EAAE,IAAI,CAAC,EAAE,EAAE;KAC/B;IACD,QAAQ,EAAE;QACR,uBAAuB,EAAE,IAAI,CAAC,EAAE,EAAE;KACnC;IACD,MAAM,EAAE;QACN,sBAAsB,EAAE,IAAI,CAAC,EAAE,EAAE;QACjC,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC7B,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC3B,mBAAmB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC9B,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE;QACvB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;QACxB,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;QACzB,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE,CAAC,QAAQ,EAAE,CAAC;QACzE,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;QAC1B,8BAA8B,EAAE,IAAI,CAAC,EAAE,EAAE;QACzC,6BAA6B,EAAE,IAAI,CAAC,EAAE,EAAE;QACxC,kCAAkC,EAAE,IAAI,CAAC,EAAE,EAAE;QAC7C,2BAA2B,EAAE,IAAI,CAAC,EAAE,EAAE;QACtC,2BAA2B,EAAE,IAAI,CAAC,EAAE,EAAE;QACtC,mCAAmC,EAAE,IAAI,CAAC,EAAE,EAAE;QAC9C,8BAA8B,EAAE,IAAI,CAAC,EAAE,EAAE;QACzC,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC7B,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;QACzB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC7B,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC3B,SAAS,EAAE;YACT,cAAc,EAAE,EAAE;YAClB,GAAG,EAAE,EAAE;SACR;QACD,8BAA8B,EAAE,IAAI,CAAC,EAAE,EAAE;QACzC,2BAA2B,EAAE,IAAI,CAAC,EAAE,EAAE;KACvC;IACD,QAAQ,EAAE;QACR,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;QACzB,eAAe,EAAE,iBAAiB,EAAE;KACrC;IACD,SAAS,EAAE;QACT,oCAAoC,EAAE,iBAAiB,EAAE;QACzD,8BAA8B,EAAE,IAAI,CAAC,EAAE,EAAE;QACzC,2BAA2B,EAAE,IAAI,CAAC,EAAE,EAAE;QACtC,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;QACzB,sBAAsB,EAAE,IAAI,CAAC,EAAE,EAAE;KAClC;IACD,SAAS,EAAE;QACT,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC3B,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;QAC/D,qBAAqB,EAAE,IAAI,CAAC,EAAE,EAAE;QAChC,wBAAwB,EAAE,iBAAiB,EAAE;QAC7C,uBAAuB,EAAE,IAAI,CAAC,EAAE,EAAE;QAClC,qBAAqB,EAAE,iBAAiB,EAAE;QAC1C,sBAAsB,EAAE,IAAI,CAAC,EAAE,EAAE;QACjC,uBAAuB,EAAE,IAAI,CAAC,EAAE,EAAE;QAClC,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC7B,mCAAmC,EAAE,IAAI,CAAC,EAAE,EAAE;QAC9C,aAAa,EAAE,EAAE;QACjB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;QACpB,EAAE,EAAE;YACF,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;YACnB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;SACrB;KACF;IACD,UAAU,EAAE;QACV,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE;KACxB;IACD,GAAG,EAAE;QACH,kBAAkB,EAAE,IAAI;QACxB,SAAS,EAAE,QAAQ;QACnB,SAAS,EAAE;YACT,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;YACpB,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;SACpB;QACD,2BAA2B,EAAE,IAAI,CAAC,EAAE,EAAE;QACtC,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE;QACvB,OAAO,EAAE,eAAe;QACxB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;KACzB;IACD,WAAW,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE;IACvC,kBAAkB,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;IAC/B,6BAA6B,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE;IAC5D,kBAAkB,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE;IAClD,QAAQ;IACR,SAAS;IACT,KAAK;IACL,uBAAuB,EAAE,SAAS,uBAAuB;QACvD,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;QAEzC,OAAO;YACL,KAAK,EAAE;gBACL,IAAI,uBAAuB;oBACzB,OAAO,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC;gBACnC,CAAC;gBACD,IAAI,uBAAuB,CAAC,GAAG;oBAC7B,MAAM,IAAI,KAAK,CACb,4EAA4E,CAC7E,CAAC;gBACJ,CAAC;gBACD,uBAAuB,CAAC,QAAQ;oBAC9B,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;oBAEtD,OAAO;wBACL,OAAO;4BACL,UAAU,CAAC,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;wBAC3D,CAAC;qBACF,CAAC;gBACJ,CAAC;aACF;YACD,MAAM;gBACJ,UAAU,CAAC,KAAK,EAAE,CAAC;YACrB,CAAC;SACF,CAAC;IACJ,CAAC;IACD,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;IACnC,gBAAgB,EAAE;QAChB,YAAY,EAAE,cAAc;KAC7B;IACD,YAAY,EAAE,SAAS,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI;QAClD,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;IAC9B,CAAC;IACD,gBAAgB,EAAE;QAChB,MAAM,EAAE,CAAC;KACV;IACD,QAAQ;IACR,eAAe;IACf,UAAU,EAAE;QACV,MAAM,EAAE,CAAC,CAAC;KACX;IACD,2BAA2B,EAAE;QAC3B,SAAS,EAAE,IAAI;KAChB;IACD,kBAAkB,EAAE;QAClB,OAAO,EAAE,EAAE;QACX,IAAI,EAAE,CAAC;KACR;IACD,oBAAoB,EAAE,SAAS,oBAAoB,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO;QAC5E,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IACD,mBAAmB,EAAE;QACnB,MAAM,EAAE,CAAC;QACT,SAAS,EAAE,CAAC;QACZ,eAAe,EAAE,CAAC;KACnB;IACD,cAAc,EAAE,SAAS,cAAc,CAAC,KAAK,EAAE,IAAI;QACjD,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACzB,CAAC;IACD,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;IACnB,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE;IACvB,UAAU,EAAE,SAAS,UAAU,CAAC,KAAK,EAAE,IAAI;QACzC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACzB,CAAC;IACD,cAAc,EAAE;QACd,QAAQ,EAAE,UAAU;QACpB,QAAQ,EAAE,UAAU;QACpB,MAAM,EAAE,QAAQ;KACjB;IACD,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;IACrB,kBAAkB,EAAE;QAClB,KAAK,EAAE,CAAC;QACR,OAAO,EAAE,CAAC;QACV,WAAW,EAAE,CAAC;QACd,IAAI,EAAE,CAAC;KACR;IACD,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;IAC5B,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;IAC5B,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;IAC5B,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;IACpB,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;IAC5B,iBAAiB,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE;IACpC,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;IACxB,aAAa,EAAE,SAAS,aAAa;QACnC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QACrB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QACrB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QACxB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;QACd,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;IAClD,CAAC;IACD,OAAO,EAAE,yBAAyB;CACnC,CAAC"}