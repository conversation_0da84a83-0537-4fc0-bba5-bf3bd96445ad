{"version": 3, "file": "insert_snippet.test.js", "sourceRoot": "", "sources": ["../../../test/integration/insert_snippet.test.js"], "names": [], "mappings": ";AAAA,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AACjC,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAC/B,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AACjC,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;AACxC,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;AACjD,MAAM,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC,iCAAiC,CAAC,CAAC;AACrE,MAAM,EACJ,gBAAgB,EAChB,0BAA0B,EAC1B,2BAA2B,GAC5B,GAAG,OAAO,CAAC,6BAA6B,CAAC,CAAC;AAC3C,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC,mCAAmC,CAAC,CAAC;AACnE,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAC9D,MAAM,EACJ,iBAAiB,EACjB,kBAAkB,EAClB,uBAAuB,EACvB,iBAAiB,EACjB,sBAAsB,GACvB,GAAG,OAAO,CAAC,+BAA+B,CAAC,CAAC;AAE7C,QAAQ,CAAC,gBAAgB,EAAE,KAAK,IAAI,EAAE;IACpC,IAAI,MAAM,CAAC;IACX,IAAI,WAAW,CAAC;IAChB,MAAM,OAAO,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;IAEtC,MAAM,CAAC,KAAK,IAAI,EAAE;QAChB,MAAM,GAAG,SAAS,CAAC;YACjB,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE;gBACnD,IAAI,SAAS,CAAC,SAAS,KAAK,iCAAiC;oBAC3D,OAAO,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC,CAAC;gBACjE,IAAI,SAAS,CAAC,SAAS,KAAK,iCAAiC;oBAC3D,OAAO,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,2BAA2B,EAAE,CAAC,CAAC;gBAClE,OAAO,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YACxD,CAAC,CAAC;YACF,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE;gBAC7C,IAAI,SAAS,CAAC,iBAAiB,KAAK,mBAAmB;oBACrD,OAAO,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;gBACvD,OAAO,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YACxD,CAAC,CAAC;SACH,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,CAAC,aAAa,EAAE,CAAC;QACvB,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,iBAAiB,EAAE,aAAa,CAAC,CAAC;QACnE,MAAM,iBAAiB,CAAC,WAAW,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,GAAG,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC,CAAC;QAC3C,MAAM,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;QAC7C,MAAM,sBAAsB,EAAE,CAAC;QAC/B,OAAO,CAAC,OAAO,EAAE,CAAC;QAClB,MAAM,kBAAkB,CAAC,WAAW,CAAC,CAAC;IACxC,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,KAAK,IAAI,EAAE;QACf,MAAM,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;QAC3D,uBAAuB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QACpC,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QAEnE,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,iBAAiB,CAAC,CAAC;IAC3F,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;QAC7D,uBAAuB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QACpC,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QAEnE,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,qBAAqB,CAAC,CAAC;IAC/F,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}