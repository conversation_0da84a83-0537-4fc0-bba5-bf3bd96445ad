{"version": 3, "file": "duo_code_suggestions.test.js", "sourceRoot": "", "sources": ["../../../test/integration/duo_code_suggestions.test.js"], "names": [], "mappings": ";AAAA,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AACjC,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAC/B,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AACjC,MAAM,0BAA0B,GAAG,OAAO,CAAC,oDAAoD,CAAC,CAAC;AACjG,MAAM,EACJ,SAAS,EACT,uBAAuB,EACvB,sBAAsB,GACvB,GAAG,OAAO,CAAC,mCAAmC,CAAC,CAAC;AACjD,MAAM,EACJ,iBAAiB,EACjB,kBAAkB,EAClB,0BAA0B,EAC1B,iBAAiB,GAClB,GAAG,OAAO,CAAC,+BAA+B,CAAC,CAAC;AAE7C,QAAQ,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;IACzC,IAAI,MAAM,CAAC;IACX,IAAI,WAAW,CAAC;IAChB,MAAM,OAAO,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;IACtC,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;IAC9E,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAEzC,MAAM,CAAC,KAAK,IAAI,EAAE;QAChB,MAAM,GAAG,SAAS,CAAC;YACjB,uBAAuB,CAAC,MAAM,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC;YAC1D,sBAAsB,CACpB,iBAAiB,EACjB,0BAA0B,EAC1B,oCAAoC,CACrC;SACF,CAAC,CAAC;QACH,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC;IAEH,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,CAAC,aAAa,EAAE,CAAC;QACvB,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,iBAAiB,EAAE,aAAa,CAAC,CAAC;QACnE,MAAM,iBAAiB,CAAC,WAAW,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,OAAO,CAAC,OAAO,EAAE,CAAC;QAClB,MAAM,kBAAkB,CAAC,WAAW,CAAC,CAAC;IACxC,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,KAAK,IAAI,EAAE;QACf,MAAM,CAAC,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC;IAEH,yEAAyE;IACzE,yEAAyE;IACzE,GAAG,CAAC,uBAAuB,EAAE,KAAK,IAAI,EAAE;QACtC,MAAM,0BAA0B,CAAC,qBAAqB,CAAC,CAAC;QAExD,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,qCAAqC,CAAC,CAAC;QAC5E,0CAA0C;QAC1C,sDAAsD;QACtD,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;QAE5C,oBAAoB;QACpB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,oCAAoC,CAAC,CAAC;QAE3E,MAAM,CAAC,WAAW;QAChB,8BAA8B;QAC9B,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,EACxE,yEAAyE,CAC1E,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}