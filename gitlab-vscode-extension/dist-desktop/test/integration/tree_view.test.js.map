{"version": 3, "file": "tree_view.test.js", "sourceRoot": "", "sources": ["../../../test/integration/tree_view.test.js"], "names": [], "mappings": ";AAAA,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AACjC,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AACjC,MAAM,EAAE,oBAAoB,EAAE,GAAG,OAAO,CAAC,oDAAoD,CAAC,CAAC;AAC/F,MAAM,EAAE,oBAAoB,EAAE,GAAG,OAAO,CAAC,oDAAoD,CAAC,CAAC;AAC/F,MAAM,EAAE,0BAA0B,EAAE,GAAG,OAAO,CAAC,mCAAmC,CAAC,CAAC;AACpF,MAAM,iBAAiB,GAAG,OAAO,CAAC,iCAAiC,CAAC,CAAC;AACrE,MAAM,wBAAwB,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC;AACzE,MAAM,YAAY,GAAG,OAAO,CAAC,2BAA2B,CAAC,CAAC;AAC1D,MAAM,EAAE,SAAS,EAAE,uBAAuB,EAAE,GAAG,OAAO,CAAC,mCAAmC,CAAC,CAAC;AAC5F,MAAM,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAElE,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAChC,IAAI,MAAM,CAAC;IACX,IAAI,YAAY,CAAC;IAEjB,MAAM,mBAAmB,GAAG;QAC1B;YACE,IAAI,EAAE,uBAAuB;YAC7B,IAAI,EAAE,QAAQ;YACd,KAAK,EAAE,gBAAgB;YACvB,KAAK,EAAE,QAAQ;YACf,UAAU,EAAE,oCAAoC;SACjD;QACD;YACE,IAAI,EAAE,+BAA+B;YACrC,IAAI,EAAE,gBAAgB;YACtB,KAAK,EAAE,gBAAgB;YACvB,KAAK,EAAE,QAAQ;YACf,UAAU,EAAE,iCAAiC;SAC9C;QACD;YACE,IAAI,EAAE,4BAA4B;YAClC,IAAI,EAAE,gBAAgB;YACtB,KAAK,EAAE,gBAAgB;YACvB,KAAK,EAAE,KAAK;YACZ,UAAU,EAAE,iCAAiC;YAC7C,UAAU,EAAE,EAAE;YACd,MAAM,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC;YAC/B,SAAS,EAAE,MAAM;YACjB,MAAM,EAAE,SAAS;YACjB,QAAQ,EAAE,SAAS;YACnB,MAAM,EAAE,OAAO;YACf,aAAa,EAAE,sBAAsB;YACrC,YAAY,EAAE,sBAAsB;YACpC,aAAa,EAAE,sBAAsB;YACrC,YAAY,EAAE,sBAAsB;YACpC,GAAG,EAAE,KAAK;YACV,OAAO,EAAE,YAAY;YACrB,IAAI,EAAE,KAAK;SACZ;QACD;YACE,IAAI,EAAE,gCAAgC;YACtC,IAAI,EAAE,QAAQ;YACd,KAAK,EAAE,gBAAgB;YACvB,KAAK,EAAE,QAAQ;YACf,UAAU,EAAE,oCAAoC;YAChD,YAAY,EAAE,IAAI;YAClB,aAAa,EAAE,CAAC,WAAW,CAAC;YAC5B,gBAAgB,EAAE,CAAC,MAAM,CAAC;YAC1B,aAAa,EAAE,SAAS;YACxB,eAAe,EAAE,SAAS;YAC1B,aAAa,EAAE,KAAK;YACpB,eAAe,EAAE,aAAa;SAC/B;KACF,CAAC;IAEF,MAAM,CAAC,KAAK,IAAI,EAAE;QAChB,MAAM,GAAG,SAAS,CAAC;YACjB,uBAAuB,CAAC,QAAQ,EAAE;gBAChC,mBAAmB,EAAE,CAAC,YAAY,CAAC;aACpC,CAAC;YACF,uBAAuB,CAAC,iCAAiC,EAAE;gBACzD,oCAAoC,EAAE,CAAC,wBAAwB,CAAC;gBAChE,0TAA0T,EACxT,CAAC,EAAE,GAAG,wBAAwB,EAAE,KAAK,EAAE,iBAAiB,EAAE,CAAC;aAC9D,CAAC;YACF,uBAAuB,CAAC,yBAAyB,EAAE;gBACjD,oCAAoC,EAAE,CAAC,iBAAiB,CAAC;gBACzD,gMAAgM,EAC9L,CAAC,EAAE,GAAG,iBAAiB,EAAE,KAAK,EAAE,oBAAoB,EAAE,CAAC;aAC1D,CAAC;SACH,CAAC,CAAC;QACH,gHAAgH;QAChH,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,MAAM,CAAC,sBAAsB,EAAE,mBAAmB,CAAC,CAAC;IAChG,CAAC,CAAC,CAAC;IAEH,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,CAAC,aAAa,EAAE,CAAC;QACvB,YAAY,GAAG,IAAI,oBAAoB,CAAC,0BAA0B,EAAE,CAAC,CAAC;QACtE,MAAM,oBAAoB,EAAE,CAAC,MAAM,EAAE,CAAC;IACxC,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,KAAK,IAAI,EAAE;QACf,MAAM,CAAC,KAAK,EAAE,CAAC;QACf,gHAAgH;QAChH,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,MAAM,CAAC,sBAAsB,EAAE,SAAS,CAAC,CAAC;IACtF,CAAC,CAAC,CAAC;IAEH,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAE7D;;OAEG;IACH,KAAK,UAAU,YAAY,CAAC,KAAK;QAC/B,MAAM,CAAC,WAAW,CAAC,GAAG,MAAM,YAAY,CAAC,WAAW,EAAE,CAAC;QACvD,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC,WAAW,EAAE,CAAC;QACnD,MAAM,CAAC,cAAc,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC;QAChF,MAAM,CACJ,cAAc,EACd,uBAAuB,KAAK,gCAAgC,UAAU,CAAC,GAAG,CACxE,CAAC,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAC1B,EAAE,CACJ,CAAC;QACF,OAAO,YAAY,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;IAClD,CAAC;IAED,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;QACnD,MAAM,kBAAkB,GAAG,MAAM,YAAY,CAAC,uBAAuB,CAAC,CAAC;QAEvE,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,MAAM,CAAC,WAAW,CAChB,kBAAkB,CAAC,CAAC,CAAC,CAAC,KAAK,EAC3B,sDAAsD,CACvD,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;QAC3D,MAAM,yBAAyB,GAAG,MAAM,YAAY,CAAC,+BAA+B,CAAC,CAAC;QAEtF,MAAM,CAAC,WAAW,CAAC,yBAAyB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACxD,MAAM,WAAW,GAAG,yBAAyB,CAAC,CAAC,CAAC,CAAC;QACjD,MAAM,MAAM,GAAG,WAAW,CAAC,WAAW,CAAC,CAAC;QACxC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,qDAAqD,CAAC,CAAC;QACxF,MAAM,CAAC,WAAW,CAChB,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAC9B,GAAG,UAAU,kDAAkD,CAChE,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;QAChD,MAAM,mBAAmB,GAAG,MAAM,YAAY,CAAC,4BAA4B,CAAC,CAAC;QAE7E,MAAM,CAAC,WAAW,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAClD,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,0BAA0B,CAAC,CAAC;IAC5F,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;QACpD,MAAM,mBAAmB,GAAG,MAAM,YAAY,CAAC,gCAAgC,CAAC,CAAC;QAEjF,MAAM,CAAC,WAAW,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAClD,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,8BAA8B,CAAC,CAAC;IAChG,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}