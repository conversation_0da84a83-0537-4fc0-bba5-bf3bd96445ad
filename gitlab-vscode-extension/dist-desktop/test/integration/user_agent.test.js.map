{"version": 3, "file": "user_agent.test.js", "sourceRoot": "", "sources": ["../../../test/integration/user_agent.test.js"], "names": [], "mappings": ";AAAA,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AACjC,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AACjC,MAAM,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;AAC5C,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;AACjD,MAAM,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC,yCAAyC,CAAC,CAAC;AAC7E,MAAM,WAAW,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAClD,MAAM,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAClE,MAAM,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC,6BAA6B,CAAC,CAAC;AAEpE,MAAM,iBAAiB,GAAG,GAAG,CAAC,EAAE;IAC9B,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAChD,MAAM,aAAa,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,IAAI,GAAG,CAAC;IAE5F,MAAM,CAAC,WAAW,CAChB,SAAS,EACT,2BAA2B,WAAW,CAAC,OAAO,WAAW,MAAM,CAAC,OAAO,YAAY,aAAa,EAAE,CACnG,CAAC;AACJ,CAAC,CAAC;AAEF,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;IACjC,IAAI,MAAM,CAAC;IACX,IAAI,eAAe,CAAC;IAEpB,MAAM,CAAC,KAAK,IAAI,EAAE;QAChB,MAAM,GAAG,WAAW,CAClB,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE;YAC3C,eAAe,GAAG,OAAO,CAAC;YAC1B,OAAO,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;QACvD,CAAC,CAAC,CACH,CAAC;QACF,MAAM,CAAC,MAAM,EAAE,CAAC;IAClB,CAAC,CAAC,CAAC;IAEH,UAAU,CAAC,GAAG,EAAE;QACd,MAAM,CAAC,aAAa,EAAE,CAAC;QACvB,eAAe,GAAG,SAAS,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,KAAK,IAAI,EAAE;QACf,MAAM,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;QACxD,MAAM,OAAO,GAAG,IAAI,aAAa,CAAC,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;QAC/E,MAAM,OAAO,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC;QAC/C,iBAAiB,CAAC,eAAe,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}