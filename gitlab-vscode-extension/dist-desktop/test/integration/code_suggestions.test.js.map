{"version": 3, "file": "code_suggestions.test.js", "sourceRoot": "", "sources": ["../../../test/integration/code_suggestions.test.js"], "names": [], "mappings": ";AAAA,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AACjC,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AACjC,MAAM,WAAW,GAAG,OAAO,CAAC,wCAAwC,CAAC,CAAC;AACtE,MAAM,EACJ,iBAAiB,EACjB,kBAAkB,EAClB,iBAAiB,GAClB,GAAG,OAAO,CAAC,+BAA+B,CAAC,CAAC;AAE7C,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;IACtC,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;QAE7E,MAAM,KAAK,GAAG,KAAK,EAAC,MAAM,EAAC,EAAE;YAC3B,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;YAC9C,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;gBAC9B,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,KAAK,EAAC,QAAQ,EAAC,EAAE,CACtC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,sCAAsC,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;QAEhG,UAAU,CAAC,KAAK,IAAI,EAAE;YACpB,MAAM,iBAAiB,CAAC,WAAW,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,SAAS,CAAC,KAAK,IAAI,EAAE;YACnB,MAAM,kBAAkB,CAAC,WAAW,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,IAAI,GAAG,gBAAgB,CAAC;YAC9B,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC;YAElB,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACzD,MAAM,WAAW,GAAG,MAAM,cAAc,CAAC,QAAQ,CAAC,CAAC;YAEnD,MAAM,CAAC,eAAe,CACpB,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAClF,EAAE,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,IAAI,GAAG,kBAAkB,CAAC;YAChC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC;YAElB,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACzD,MAAM,WAAW,GAAG,MAAM,cAAc,CAAC,QAAQ,CAAC,CAAC;YAEnD,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;YAEjE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YAClD,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,eAAe,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,IAAI,GAAG,SAAS,CAAC;YACvB,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC;YAElB,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACzD,MAAM,WAAW,GAAG,MAAM,cAAc,CAAC,QAAQ,CAAC,CAAC;YAEnD,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,MAAM,IAAI,GAAG,OAAO,CAAC;YACrB,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC;YAElB,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3C,MAAM,WAAW,GAAG,MAAM,cAAc,CAAC,QAAQ,CAAC,CAAC;YAEnD,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,IAAI,GAAG,iBAAiB,CAAC;YAC/B,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC;YAElB,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACzD,MAAM,WAAW,GAAG,MAAM,cAAc,CAAC,QAAQ,CAAC,CAAC;YAEnD,MAAM,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;YACrD,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,IAAI,GAAG,uFAAuF,CAAC;YACrG,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC;YAElB,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACzD,MAAM,WAAW,GAAG,MAAM,cAAc,CAAC,QAAQ,CAAC,CAAC;YAEnD,MAAM,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YAC7C,MAAM,CAAC,eAAe,CACpB,KAAK,CAAC,SAAS,EACf,IAAI,CAAC,MAAM,GAAG,CAAC,EACf,4DAA4D,CAC7D,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}