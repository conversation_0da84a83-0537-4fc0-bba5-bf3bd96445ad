{"version": 3, "file": "extension_settings.test.js", "sourceRoot": "", "sources": ["../../../test/integration/extension_settings.test.js"], "names": [], "mappings": ";AAAA,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AACjC,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AACjC,MAAM,qBAAqB,GAAG,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAE7F,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;IAClC,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,MAAM,2BAA2B,GAAG,2BAA2B,CAAC;QAChE,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;YAChF,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YAC5D,MAAM,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChD,MAAM,iBAAiB,GAAG,qBAAqB,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;YAE1F,KAAK,MAAM,QAAQ,IAAI,iBAAiB,EAAE,CAAC;gBACzC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE,GAAG,QAAQ,aAAa,CAAC,CAAC;YACpE,CAAC;YACD,MAAM,CAAC,EAAE,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,6BAA6B,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}