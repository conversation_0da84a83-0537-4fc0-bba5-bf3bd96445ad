# frozen_string_literal: true

module GDK
  # GDK Commands
  module Command
    # This is a list of existing supported commands and their associated
    # implementation class
    COMMANDS = {
      'cells' => -> { GDK::Command::Cells },
      'cleanup' => -> { GDK::Command::Cleanup },
      'clickhouse' => -> { GDK::Command::Clickhouse },
      'config' => -> { GDK::Command::Config },
      'console' => -> { GDK::Command::Console },
      'bao' => -> { GDK::Command::Bao },
      'debug-info' => -> { GDK::Command::Removed.new('Use `gdk report` instead.') },
      'diff-config' => -> { GDK::Command::DiffConfig },
      'doctor' => -> { GDK::Command::Doctor },
      'env' => -> { GDK::Command::Env },
      'install' => -> { GDK::Command::Install },
      'kill' => -> { GDK::Command::Kill },
      'help' => -> { GDK::Command::Help },
      '-help' => -> { GDK::Command::Help },
      '--help' => -> { GDK::Command::Help },
      '-h' => -> { GDK::Command::Help },
      nil => -> { GDK::Command::Help },
      'measure' => -> { GDK::Command::MeasureUrl },
      'measure-workflow' => -> { GDK::Command::MeasureWorkflow },
      'open' => -> { GDK::Command::Open },
      'telemetry' => -> { GDK::Command::Telemetry },
      'psql' => -> { GDK::Command::Psql },
      'psql-geo' => -> { GDK::Command::PsqlGeo },
      'predictive' => -> { GDK::Command::Predictive },
      'predictive:jest' => -> { GDK::Command::Removed.new('Use `gdk predictive --jest` instead.') },
      'predictive:rspec' => -> { GDK::Command::Removed.new('Use `gdk predictive --rspec` instead.') },
      'pristine' => -> { GDK::Command::Pristine },
      'rails' => -> { GDK::Command::Rails },
      'rake' => -> { GDK::Command::Rake },
      'reconfigure' => -> { GDK::Command::Reconfigure },
      'redis-cli' => -> { GDK::Command::RedisCli },
      'report' => -> { GDK::Command::Report },
      'reset-data' => -> { GDK::Command::ResetData },
      'reset-praefect-data' => -> { GDK::Command::ResetPraefectData },
      'reset-registry-data' => -> { GDK::Command::ResetRegistryData },
      'import-registry-data' => -> { GDK::Command::ImportRegistryData },
      'restart' => -> { GDK::Command::Restart },
      'sandbox' => -> { GDK::Command::Sandbox },
      'send-telemetry' => -> { GDK::Command::SendTelemetry },
      'start' => -> { GDK::Command::Start },
      'status' => -> { GDK::Command::Status },
      'stop' => -> { GDK::Command::Stop },
      'switch' => -> { GDK::Command::Switch },
      'tail' => -> { GDK::Command::Tail },
      'truncate-legacy-tables' => -> { GDK::Command::TruncateLegacyTables },
      'update' => -> { GDK::Command::Update },
      'version' => -> { GDK::Command::Version },
      '-version' => -> { GDK::Command::Version },
      '--version' => -> { GDK::Command::Version }
    }.freeze

    # Entry point for gem/bin/gdk.
    #
    # It must return true/false or an exit code.
    def self.run(argv)
      name = argv.shift
      command = ::GDK::Command::COMMANDS[name]

      if command
        klass = command.call

        check_gem_version!
        validate_config! if klass.validate_config?

        if klass == ::GDK::Command::Rake
          name = "gdk:rake"
        else
          check_asdf_usage
        end

        begin
          run = -> { klass.new.run(argv) }
          result = klass.track_telemetry?(argv) ? GDK::Telemetry.with_telemetry(name, &run) : run.call
        rescue UserInteractionRequired => e
          e.print!
          exit 1
        ensure
          check_workspace_setup_complete(name)
        end

        exit result
      else
        suggestions = DidYouMean::SpellChecker.new(dictionary: ::GDK::Command::COMMANDS.keys).correct(name)
        message = ["#{name} is not a GDK command"]

        if suggestions.any?
          message << ', did you mean - '
          message << suggestions.map { |suggestion| "'gdk #{suggestion}'" }.join(' or ')
          message << '?'
        else
          message << '.'
        end

        GDK::Output.warn message.join
        GDK::Output.puts

        GDK::Output.info "See 'gdk help' for more detail."
        false
      end
    end

    def self.validate_config!
      GDK.config.validate!
      GDK::Services.enabled.each(&:validate!)
      nil
    rescue StandardError => e
      GDK::Output.error("Your GDK configuration is invalid.\n\n", e)
      GDK::Output.puts(e.message, stderr: true)
      abort('')
    end

    def self.check_gem_version!
      return if Gem::Version.new(GDK::GEM_VERSION) >= Gem::Version.new(GDK::REQUIRED_GEM_VERSION)

      GDK::Output.warn("You are running an old version of the `gitlab-development-kit` gem (#{GDK::GEM_VERSION})")
      GDK::Output.info("Please update your `gitlab-development-kit` to version #{GDK::REQUIRED_GEM_VERSION}:")
      GDK::Output.info("gem install gitlab-development-kit -v #{GDK::REQUIRED_GEM_VERSION}")
      GDK::Output.puts
    end

    def self.check_workspace_setup_complete(command_name = nil)
      # Set by Workspaces
      return unless ENV['GL_WORKSPACE_DOMAIN_TEMPLATE']
      return if GDK.config.__cache_dir.join('.gdk_setup_complete').exist?

      # Skip warnings for config commands as they break scripts that use
      # command substitution like $(gdk config get ...)
      return if command_name == 'config'

      GDK::Output.puts
      GDK::Output.warn('GDK setup in progress...')
      GDK::Output.puts('Run `tail -f /projects/workspace-logs/poststart-stdout.log` to watch the progress.')
    end

    def self.check_asdf_usage
      # When the user opts out of 'asdf'
      return if GDK.config.asdf.opt_out? || !Dependencies.asdf_available?

      # When the user opts out of any kind of tool management
      return if GDK.config.user_defined?('tool_version_manager', 'enabled') &&
        !GDK.config.tool_version_manager.enabled?

      GDK::Output.error <<~MSG
        You are using asdf to manage GDK dependencies. GDK dropped support for asdf on #{GDK::Output.wrap_in_color('July 31st, 2025', Output::COLOR_CODE_YELLOW)}.

        To continue using GDK, migrate to mise by running:
          `gdk rake mise:migrate`

        Migration instructions available at:
          https://gitlab-org.gitlab.io/gitlab-development-kit/howto/mise/#how-to-migrate
      MSG
      GDK::Telemetry.send_custom_event('asdf-error', true)
      exit 1
    end
  end
end
