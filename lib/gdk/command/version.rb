# frozen_string_literal: true

module GDK
  module Command
    # Handles `gdk version` command execution
    class Version < BaseCommand
      help 'Print current GDK version'

      # Allow invalid gdk.yml.
      def self.validate_config?
        false
      end

      def run(_ = [])
        GDK::Output.puts("#{GDK::VERSION} (#{version.current_commit.sha})")
        diff_message = version.diff_message
        GDK::Output.puts(diff_message) if diff_message

        true
      end

      private

      def version
        @version ||= ::GDK::VersionChecker.new(
          service_path: GDK.root
        )
      end
    end
  end
end
