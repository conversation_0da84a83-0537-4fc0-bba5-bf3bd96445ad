# frozen_string_literal: true

require 'terminal-table'

module GDK
  module Command
    # Display status of all enabled services or specified ones only
    class Status < BaseCommand
      help 'See status of all services'

      STATE_COLORS = {
        'up' => GDK::Output::COLOR_CODE_GREEN,
        'down' => GDK::Output::COLOR_CODE_BLUE,
        'down (wants up)' => GDK::Output::COLOR_CODE_RED
      }.freeze

      def run(args = [])
        rows = []
        Runit.status(args).each do |service|
          state = out.wrap_in_color("#{service.state} #{service.duration}s", STATE_COLORS[service.state])
          rows << [
            service.pid.zero? ? '' : service.pid,
            state,
            out.wrap_in_color(service.name, out::COLOR_CODE_YELLOW)
          ]
        end

        table = Terminal::Table.new(
          headings: %w[PID STATUS SERVICE],
          rows: rows,
          style: { padding_right: 2 }
        )

        out.puts table

        print_ready_message if args.empty?
        true
      end
    end
  end
end
