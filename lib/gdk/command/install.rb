# frozen_string_literal: true

module GDK
  module Command
    # Handles `gdk install` command execution
    #
    # This command accepts the following parameters:
    # - gitlab_repo=<url to repository> (defaults to: "https://gitlab.com/gitlab-org/gitlab")
    # - telemetry_enabled=<true|false> (defaults to: false)
    class Install < BaseCommand
      help 'Install everything'

      def run(args = [])
        args.each do |arg|
          case arg
          when /^telemetry_enabled=(true|false)$/
            GDK::Telemetry.update_settings(Regexp.last_match(1) == 'true' ? 'y' : 'n')
          end
        end

        result = GDK.make('install', *args)

        unless result.success?
          GDK::Output.error('Failed to install.', result.stderr_str)
          display_help_message
        end

        Announcements.new.cache_all if result.success?

        result.success?
      end
    end
  end
end
