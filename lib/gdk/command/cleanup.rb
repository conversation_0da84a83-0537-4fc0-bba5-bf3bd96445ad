# frozen_string_literal: true

require 'rake'

module GDK
  module Command
    class Cleanup < BaseCommand
      help 'Truncate log files and remove any unnecessarily installed dependencies'

      def run(_ = [])
        return true unless continue?

        execute
      end

      private

      def continue?
        GDK::Output.warn('About to perform the following actions:')
        GDK::Output.puts(stderr: true)
        GDK::Output.puts('- Truncate gitlab/log/* files', stderr: true)
        GDK::Output.puts("- Truncate #{GDK::Services::GitlabHttpRouter::LOG_PATH} file", stderr: true)

        GDK::Output.puts(stderr: true)

        return true if ENV.fetch('GDK_CLEANUP_CONFIRM', 'false') == 'true' || !GDK::Output.interactive?

        result = GDK::Output.prompt('Are you sure? [y/N]').match?(/\Ay(?:es)*\z/i)
        GDK::Output.puts(stderr: true)

        result
      end

      def execute
        truncate_log_files
        truncate_http_router_log_files
      rescue StandardError => e
        GDK::Output.error(e)
        false
      end

      def truncate_log_files
        run_rake('gitlab:truncate_logs', 'false')
      end

      def truncate_http_router_log_files
        run_rake('gitlab:truncate_http_router_logs', 'false')
      end
    end
  end
end
