# frozen_string_literal: true

module GDK
  module Command
    # Handles `gdk tail` command execution
    #
    # This command accepts the following subcommands:
    # - --help
    class Tail < BaseCommand
      help 'Tail logs for all services (stdout and stderr only)'
      help 'redis postgresql', 'Tail logs of specific services'

      OUTPUT = <<~MSG
        Usage: gdk tail [[--help] | [<log_or_shortcut>[ <...>]]

        Tail command:

          gdk tail                                                  # Tail all log files (stdout and stderr only)
          gdk tail <log_or_shortcut>[ <...>]                        # Tail specified log files (stdout and stderr only)
          gdk tail --help                                           # Print this help text

        Available logs:

          %<logs>s

        Shortcuts:

          %<shortcuts>s

        To contribute to GitLab, see
        https://docs.gitlab.com/ee/development/index.html.
      MSG

      def run(args = [])
        return print_help if args.intersect?(['--help', '-h'])

        Runit.tail(args)
      end

      private

      def print_help
        aligned_logs = Runit::LOG_DIR.children.map(&:basename).sort.join("\n  ")

        # Keep inline with `Tail command:`
        width = [Runit::SERVICE_SHORTCUTS.keys.max_by(&:length).length + 20, 57].max
        shortcuts = Runit::SERVICE_SHORTCUTS.map { |sc| format("%-*s # %s", width, *sc) }
        aligned_shortcuts = shortcuts.sort.join("\n  ")

        output = format(OUTPUT, logs: aligned_logs, shortcuts: aligned_shortcuts)

        GDK::Output.puts(output)

        true
      end
    end
  end
end
