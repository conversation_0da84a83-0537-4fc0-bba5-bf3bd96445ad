# frozen_string_literal: true

require 'time'

module GDK
  module Command
    # Switches to a branch in the monolith and sets up the environment for it.
    class Switch < BaseCommand
      help '<branch>', 'Switch to a branch and set up services (similar to `gdk update`)'

      def run(args = [])
        branch = args.pop

        unless branch
          out.warn('Usage: gdk switch [BRANCH_NAME]')
          return false
        end

        setup_sandbox(branch)

        # Don't save config after this
        config.bury!('gitlab.default_branch', branch)

        success = GDK::Hooks.with_hooks(config.gdk.update_hooks, 'gdk switch') do
          run_rake('update_branch', branch)
        end

        unless success
          GDK::Output.error('Failed to switch branches.', report_error: false)
          display_help_message
          return false
        end

        color_branch = out.wrap_in_color(branch, Output::COLOR_CODE_YELLOW)
        GDK::Output.success("Switched to #{color_branch}.")

        true
      rescue Support::Rake::TaskWithLogger::LoggerError => e
        e.print!
        false
      end

      private

      def setup_sandbox(branch)
        return unless config.gdk.sandbox.managed

        if branch == config.gitlab.default_branch
          sandbox_manager.disable! if sandbox_manager.enabled?
        else
          sandbox_manager.enable! unless sandbox_manager.enabled?
        end
      end
    end
  end
end
