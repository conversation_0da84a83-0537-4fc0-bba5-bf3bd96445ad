# frozen_string_literal: true

module GDK
  module Command
    # Handles `gdk rails <command> [<args>]` command execution
    class Rails < BaseCommand
      help '<command> [<args>]', 'Execute <command> with Rails command'

      def run(args = [])
        GDK::Output.abort('Usage: gdk rails <command> [<args>]', report_error: false) if args.empty?

        execute_command!(args)
      end

      private

      def execute_command!(args)
        exec(
          GDK.config.env,
          *generate_command(args)
        )
      end

      def generate_command(args)
        %w[support/tool-version-manager-exec gitlab rails] + args
      end
    end
  end
end
