---
title: How to use GitLab Development Kit
---

Before using the GDK features below, see [install instructions](../_index.md).

Main purpose of GitLab Development Kit is to make GitLab development easier.
Please see [GitLab Contributor documentation](https://docs.gitlab.com/ee/development/index.html)
to learn how to contribute to GitLab.

## Basic

- [Browse your development GitLab server](browse.md)
- [GDK commands](../gdk_commands.md)
- [Installation, configuration, and development troubleshooting](../troubleshooting/_index.md)
- Command help with `gdk help`

## Tips and tricks

- [Local network binding](local_network.md)
- [Load testing](load_testing.md)
- [Configuration](../configuration.md)

## Special topics

- [Announcements](announcements.md)
- [Asset Proxy / Camo Server](asset_proxy.md)
- [Change GitLab Version](gitlab_version.md)
- [Cells](cells.md)
- [Database load balancing](database_load_balancing.md)
- [Debugging with Pry](pry.md)
- [Dependency Proxy](dependency_proxy.md)
- [Environment variables](../configuration.md#environment-variables)
- [Elasticsearch](elasticsearch.md)
- [Email](email.md)
- [Enable Shell completion](shell_completion.md)
- [End to End Test Configuration](end_to_end_test_configuration.md)
- [Feature flags](feature_flags.md)
- [.gdkrc.custom](gdkrc.custom.md)
- [GDK in Gitpod](gitpod.md)
- [Git LFS](lfs.md)
- [Git push options](git_push_options.md)
- [GitLab AI Gateway](gitlab_ai_gateway.md)
- [GitLab Observability Backend (formerly opstrace)](gitlab_observability_backend.md)
- [GitLab Geo](geo.md)
- [GitLab.com OAuth2](gitlab-oauth2.md)
- [Gitaly and Praefect](gitaly.md)
- [Google OAuth2](google-oauth2.md)
- [HTTPS](nginx.md)
- [Improving dependency management performance with `mise`](mise.md)
- [Kerberos](kerberos.md)
- [Kubernetes](kubernetes/_index.md)
- [LDAP](ldap.md)
- [Lefthook](lefthook.md)
- [NFS](nfs.md)
- [PostgreSQL](postgresql.md)
- [Puma](puma.md)
- [Preview and test the documentation site locally](gitlab_docs.md)
- [Preview GitLab changes](preview_gitlab_changes.md)
- [Product Analytics](product_analytics.md)
- [Repository graph](repository_graph.md)
- [SSH](ssh.md)
- [Serverless (Knative)](serverless.md)
- [Service Desk and MailRoom](service_desk_mail_room.md)
- [Simulate slow or broken repository storage](simulate_storage.md)
- [Siphon](siphon.md)
- [Snowplow Micro](snowplow_micro.md)
- [Start a Rails console](rails_console.md)
- [Tracing and Jaeger](https://docs.gitlab.com/ee/development/distributed_tracing.html#using-jaeger-in-the-gitlab-development-kit)
- [Update external dependencies](update_external_dependencies.md)
- [Use Container Registry with GDK](registry.md)
- [Use GitLab Runner with GDK](runner.md)
- [Use Prometheus with GDK](prometheus/_index.md)
- [Use Caddy to make GDK on a virtual machine publicly accessible](public_gdk.md)
- [OpenBao](openbao.md)
