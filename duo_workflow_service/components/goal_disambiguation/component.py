# pylint: disable=direct-environment-variable-reference

import os
import uuid
from datetime import datetime, timezone
from enum import StrEnum
from functools import partial
from typing import Annotated, Any, List, Literal, Union

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, ToolMessage
from langgraph.graph import END, StateGraph
from langgraph.types import interrupt
from langsmith import traceable

from duo_workflow_service.agents.handover import HandoverAgent
from duo_workflow_service.components.base import BaseComponent
from duo_workflow_service.entities.event import WorkflowEvent, WorkflowEventType
from duo_workflow_service.entities.state import (
    MessageTypeEnum,
    ToolStatus,
    UiChatLog,
    WorkflowState,
    WorkflowStatusEnum,
)
from duo_workflow_service.tools.request_user_clarification import (
    RequestUserClarificationTool,
)

from ...tools import HandoverTool

_AGENT_NAME = "clarity_judge"

_MIN_CLARITY_THRESHOLD = 4
_MIN_CLARITY_GRADE = "CLEAR"


class Routes(StrEnum):
    UNCLEAR = "unclear"
    CLEAR = "clear"
    CONTINUE = "continue"
    BACK = "back"
    STOP = "stop"


class GoalDisambiguationComponent(BaseComponent):
    def __init__(self, allow_agent_to_request_user: bool, **kwargs: Any):
        super().__init__(**kwargs)

        self.allow_agent_to_request_user = self._allowed_to_clarify(
            allow_agent_to_request_user
        )

    def attach(
        self,
        graph: StateGraph,
        component_exit_node: str,
        component_execution_state: WorkflowStatusEnum,
        graph_termination_node: str = END,
    ) -> Annotated[str, "Entry node name"]:
        if not self.allow_agent_to_request_user:
            return component_exit_node

        toolset = self.tools_registry.toolset(
            [RequestUserClarificationTool.tool_title, HandoverTool.tool_title]
        )
        task_clarity_judge = self.prompt_registry.get_on_behalf(
            self.user,
            "workflow/goal_disambiguation",
            "^1.0.0",
            tools=toolset.bindable,  # type: ignore[arg-type]
            workflow_id=self.workflow_id,
            workflow_type=self.workflow_type,
            http_client=self.http_client,
            prompt_template_inputs={
                "clarification_tool": RequestUserClarificationTool.tool_title,
            },
        )

        # Wrap the agent run method with LangSmith tracing
        traced_agent_run = self._create_traced_agent_run(task_clarity_judge.run)
        graph.add_node("task_clarity_check", traced_agent_run)
        entrypoint = "task_clarity_check"

        task_clarity_handover = HandoverAgent(
            new_status=WorkflowStatusEnum.PLANNING,
            handover_from=_AGENT_NAME,
            include_conversation_history=True,
        )
        graph.add_conditional_edges(
            "task_clarity_check",
            self._clarification_required,
            {
                Routes.CLEAR: "task_clarity_handover",
                Routes.UNCLEAR: "task_clarity_request_clarification",
                Routes.STOP: graph_termination_node,
            },
        )

        # Wrap other node methods with tracing
        traced_ask_question = self._create_traced_ask_question()
        traced_handle_clarification = self._create_traced_handle_clarification(component_execution_state)

        graph.add_node("task_clarity_request_clarification", traced_ask_question)
        graph.add_edge(
            "task_clarity_request_clarification", "task_clarity_fetch_user_response"
        )
        graph.add_node(
            "task_clarity_fetch_user_response",
            traced_handle_clarification,
        )
        graph.add_conditional_edges(
            "task_clarity_fetch_user_response",
            self._clarification_provided,
            {
                Routes.BACK: "task_clarity_fetch_user_response",
                Routes.CONTINUE: "task_clarity_check",
                Routes.STOP: graph_termination_node,
            },
        )

        graph.add_node("task_clarity_handover", task_clarity_handover.run)
        graph.add_edge("task_clarity_handover", component_exit_node)

        return entrypoint

    @traceable(name="Goal_Disambiguation_Agent")
    def _create_traced_agent_run(self, original_run_method):
        """Create a traced version of the agent run method for LangSmith observability."""
        return original_run_method

    @traceable(name="Goal_Disambiguation_Ask_Question")
    def _create_traced_ask_question(self):
        """Create a traced version of the ask question method."""
        return self._ask_question

    def _create_traced_handle_clarification(self, component_execution_state):
        """Create a traced version of the handle clarification method."""

        @traceable(
            name="Goal_Disambiguation_Handle_Clarification",
            run_type="tool",
            metadata={
                "agent_type": "goal_disambiguation",
                "workflow_id": self.workflow_id,
                "operation": "handle_user_clarification",
                "component_execution_state": component_execution_state.value
            }
        )
        async def traced_handle_clarification(state: WorkflowState):
            inputs = {
                "current_status": state.get("status"),
                "has_last_human_input": state.get("last_human_input") is not None,
                "component_execution_state": component_execution_state.value,
                "run_id": str(uuid.uuid4())
            }

            try:
                result = await self._handle_clarification(component_execution_state, state)

                outputs = {
                    "conversation_updates": list(result.get("conversation_history", {}).keys()) if isinstance(result, dict) else [],
                    "ui_chat_log_updates": len(result.get("ui_chat_log", [])) if isinstance(result, dict) else 0,
                    "status_change": result.get("status") if isinstance(result, dict) else None
                }

                return result

            except Exception as e:
                outputs = {
                    "error": str(e),
                    "error_type": type(e).__name__
                }
                raise

        return traced_handle_clarification

    def _extract_tool_calls_from_result(self, result):
        """Extract tool call information from agent result for tracing."""
        if not isinstance(result, dict):
            return []

        conversation_history = result.get("conversation_history", {})
        tool_calls = []

        for agent_name, messages in conversation_history.items():
            for message in messages:
                if hasattr(message, 'tool_calls') and message.tool_calls:
                    for tool_call in message.tool_calls:
                        tool_calls.append({
                            "agent": agent_name,
                            "tool_name": tool_call.get("name"),
                            "tool_id": tool_call.get("id")
                        })

        return tool_calls

    def _extract_clarity_decision(self, result):
        """Extract clarity decision from agent result for tracing."""
        if not isinstance(result, dict):
            return None

        conversation_history = result.get("conversation_history", {})
        agent_messages = conversation_history.get(_AGENT_NAME, [])

        if not agent_messages:
            return None

        last_message = agent_messages[-1]
        if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
            for tool_call in last_message.tool_calls:
                if tool_call.get("name") == "request_user_clarification_tool":
                    args = tool_call.get("args", {})
                    return {
                        "clarity_verdict": args.get("clarity_verdict"),
                        "clarity_score": args.get("clarity_score"),
                        "needs_clarification": True
                    }
                elif tool_call.get("name") == "handover_tool":
                    return {
                        "clarity_verdict": "CLEAR",
                        "needs_clarification": False,
                        "handover": True
                    }

        return {"needs_clarification": False, "no_tool_calls": True}

    def _allowed_to_clarify(self, allow_agent_to_request_user: bool) -> bool:
        return (
            os.environ.get("FEATURE_GOAL_DISAMBIGUATION", "False").lower()
            in ("true", "1", "t")
            and os.environ.get("USE_MEMSAVER", "False").lower()
            not in ("true", "1", "t")
            and allow_agent_to_request_user
        )

    async def _ask_question(
        self, state: WorkflowState
    ) -> dict[str, Union[list[UiChatLog], WorkflowStatusEnum]]:
        last_message: AIMessage = state["conversation_history"][_AGENT_NAME][-1]  # type: ignore
        if last_message.tool_calls is None:
            return {"ui_chat_log": []}

        tool_call = last_message.tool_calls[0]["args"]

        recommendations = (
            "\n".join(
                [
                    f"{i}. {recommendation}"
                    for i, recommendation in enumerate(tool_call["recommendations"], 1)
                ]
            )
            if isinstance(tool_call["recommendations"], list)
            else f"1. {tool_call['recommendations']}"
        )

        response = f"{tool_call['response']}\n" if tool_call.get("response") else ""

        return {
            "ui_chat_log": [
                UiChatLog(
                    message_type=MessageTypeEnum.REQUEST,
                    message_sub_type=None,
                    content=f"""{response}{tool_call.get("message", "")}

I'm ready to help with your project but I need a few key details:

{recommendations}""".strip(),
                    timestamp=datetime.now(timezone.utc).isoformat(),
                    status=None,
                    correlation_id=None,
                    tool_info=None,
                    additional_context=None,
                )
            ],
            "status": WorkflowStatusEnum.INPUT_REQUIRED,
        }

    async def _handle_clarification(
        self, component_execution_state: WorkflowStatusEnum, state: WorkflowState
    ) -> dict[
        str, Union[list[UiChatLog], WorkflowStatusEnum, dict[str, list[BaseMessage]]]
    ]:
        event: WorkflowEvent = interrupt(
            "Workflow interrupted; waiting for user's clarification."
        )

        if event["event_type"] == WorkflowEventType.STOP:
            return {"status": WorkflowStatusEnum.CANCELLED}

        if event["event_type"] != WorkflowEventType.MESSAGE:
            return {"status": WorkflowStatusEnum.INPUT_REQUIRED}

        message = event["message"]
        ui_chat_logs = [
            UiChatLog(
                correlation_id=(
                    event["correlation_id"] if event.get("correlation_id") else None
                ),
                message_type=MessageTypeEnum.USER,
                message_sub_type=None,
                content=message,
                timestamp=datetime.now(timezone.utc).isoformat(),
                status=ToolStatus.SUCCESS,
                tool_info=None,
                additional_context=None,
            )
        ]

        last_message = state["conversation_history"][_AGENT_NAME][-1]
        messages: List[BaseMessage] = [
            ToolMessage(
                content=f"{message}",
                tool_call_id=tool_call.get("id"),
            )
            for tool_call in getattr(last_message, "tool_calls", [])
        ]
        messages.append(
            HumanMessage(
                content=(
                    f"Review my feedback in the {RequestUserClarificationTool.tool_title} tool response.\n"
                    "Answer all question within my feedback, and finally reevaluate clarity."
                )
            )
        )

        return {
            "status": component_execution_state,
            "ui_chat_log": ui_chat_logs,
            "conversation_history": {_AGENT_NAME: messages},
        }

    @traceable(name="Goal_Disambiguation_Clarification_Router")
    def _clarification_required(
        self, state: WorkflowState
    ) -> Literal[Routes.CLEAR, Routes.UNCLEAR, Routes.STOP]:
        if state["status"] == WorkflowStatusEnum.CANCELLED:
            return Routes.STOP

        conversation_history = state.get("conversation_history", {})
        agent_messages = conversation_history.get(_AGENT_NAME, [])

        if not agent_messages:
            return Routes.CLEAR

        last_message: AIMessage = agent_messages[-1]  # type: ignore

        if last_message.tool_calls is None or len(last_message.tool_calls) == 0:
            return Routes.CLEAR

        tool_call = last_message.tool_calls[0]  # type: ignore
        tool_args = tool_call["args"]
        tool_name = tool_call["name"]

        if tool_name == "request_user_clarification_tool":
            clarity_verdict = tool_args.get("clarity_verdict")
            clarity_score = tool_args.get("clarity_score", 0)

            if clarity_verdict == _MIN_CLARITY_GRADE or clarity_score >= _MIN_CLARITY_THRESHOLD:
                return Routes.CLEAR
            else:
                return Routes.UNCLEAR

        if tool_name == "handover_tool":
            return Routes.CLEAR

        return Routes.UNCLEAR

    @traceable(
        name="Goal_Disambiguation_Clarification_Provided_Router",
        run_type="chain",
        metadata={
            "agent_type": "goal_disambiguation",
            "operation": "route_clarification_provided"
        }
    )
    def _clarification_provided(
        self, state: WorkflowState
    ) -> Literal[Routes.CONTINUE, Routes.BACK, Routes.STOP]:
        # Log routing inputs
        current_status = state.get("status")

        inputs = {
            "current_status": current_status,
            "run_id": str(uuid.uuid4())
        }

        if state["status"] == WorkflowStatusEnum.CANCELLED:
            outputs = {
                "route_decision": Routes.STOP,
                "reason": "workflow_cancelled"
            }
            return Routes.STOP

        if state["status"] == WorkflowStatusEnum.INPUT_REQUIRED:
            outputs = {
                "route_decision": Routes.BACK,
                "reason": "input_still_required"
            }
            return Routes.BACK

        outputs = {
            "route_decision": Routes.CONTINUE,
            "reason": "ready_to_continue"
        }
        return Routes.CONTINUE
