_gdk_root()
{
  local root
  root="$(cd "$(dirname .)" || exit ; pwd -P)"

  while [ "$root" != "/" ]; do
    if [ -e "$root/GDK_ROOT" ]; then
      echo "$root"
      return
    fi

    root=$(dirname "$root")
  done
}

_gdk()
{
  local index cur action root words
  local j k

  index="$COMP_CWORD"
  cur="${COMP_WORDS[index]}"
  action="${COMP_WORDS[1]}"
  root=$(_gdk_root)
  cache="$root/tmp/.gdk_completion_commands"

  if [ "$index" = 1 ]; then
    if [ -z "$root" ]; then
      words="help version init"
    else
      if [ ! -f "$cache" ]; then
        gdk help --completion > "$cache"
      fi
      words=$(< "$cache")
    fi
  else
    case "$action" in
      init)
        [ -n "$BASH_VERSION" ] && compopt -o nospace
        words=$(compgen -o dirnames -- "$cur")
        ;;
    esac

    if [ -n "$root" ]; then
      case "$action" in
        start|stop|status|restart|tail)
          words=$(ls "$root/services")
          ;;
        psql)
          if [ "$index" = 2 ]; then
            words="-d"
          elif [ "$index" = 3 ]; then
            words="gitlabhq_development gitlabhq_test"
          fi
          ;;
        redis-cli)
          if [ -n "$BASH_VERSION" ] && type -t _command_offset >/dev/null; then
            _command_offset 1
            return
          fi
          ;;
      esac
    fi
  fi

  for j in $(compgen -W "$words" -- "$cur")
  do
    COMPREPLY[k++]=$j
  done
}

complete -F _gdk gdk
