FROM debian:bookworm

ARG git_checkout_branch
ENV GIT_CHECKOUT_BRANCH=$git_checkout_branch

WORKDIR /gitlab-gdk

COPY additional_packages_*.txt /gitlab-gdk/

# Prepare the 'raw' container image for GDK
RUN ARCH=$(uname -m) \
    && apt-get update \
    && apt-get install -y \
      chromium-driver \
      cmake \
      curl \
      g++ \
      git \
      gpg \
      libssl-dev \
      locales \
      make \
      pkg-config \
      software-properties-common \
      sudo \
      vim \
      $(sed -e 's/#.*//' "additional_packages_${ARCH}.txt") \
    && rm -rf /var/lib/apt/lists/* \
    && rm additional_packages_*.txt \
    && useradd gdk -u 5001 -m -s /bin/bash \
    && chown -Rv gdk:gdk /gitlab-gdk \
    && sed -i "s|# en_US.UTF-8 UTF-8|en_US.UTF-8 UTF-8|" /etc/locale.gen \
    && sed -i "s|# C.UTF-8 UTF-8|C.UTF-8 UTF-8|" /etc/locale.gen \
    && locale-gen C.UTF-8 en_US.UTF-8

COPY --chmod=600 --chown=root:root ./gdk_sudoers /etc/sudoers.d/gdk_sudoers
COPY --chmod=755 --chown=gdk:gdk ./gdk-container-startup.sh /home/<USER>/gdk-container-startup.sh

USER gdk

SHELL ["/bin/bash", "-c"]

# See https://gitlab.com/gitlab-org/gitlab-development-kit/-/issues/2611
ENV VITE_RUBY_SKIP_PROXY=false

# Prepare Mise
RUN mkdir -p ~/.config/mise \
    && curl https://mise.run | sh \
    && eval "$(~/.local/bin/mise activate bash)" \
    && echo "# Added to support mise for GDK" >> ~/.bashrc \
    && echo "eval \"\$(~/.local/bin/mise activate bash)\"" >> ~/.bashrc

# Prepare GDK and prereqs (bootstrap)
RUN eval "$(~/.local/bin/mise activate bash)" \
    && git clone https://gitlab.com/gitlab-org/gitlab-development-kit.git \
    && cd gitlab-development-kit \
    && if [[ -n "${GIT_CHECKOUT_BRANCH:-}" ]]; then git checkout "${GIT_CHECKOUT_BRANCH}"; fi \
    && ln -s /gitlab-gdk/gitlab-development-kit ~/gitlab-development-kit \
    && echo -e "---\ntool_version_manager:\n  enabled: true\n" > gdk.yml \
    && make bootstrap \
    # GDK bootstrap runs _more_ apt actions, lets clean out the cache
    && sudo apt-get clean \
    && sudo rm -rf /var/lib/apt/lists/*

# Prepare the container for SSH access
# We do some of these steps down here, because it's _after_ GDK installation
RUN mkdir -p ~/.ssh \
    && cp /gitlab-gdk/gitlab-development-kit/support/gdk-in-a-box/gdk.local_rsa.pub ~/.ssh/authorized_keys \
    && chmod 600 ~/.ssh/authorized_keys \
    && sudo mkdir -p /run/sshd \
    && sudo mv /etc/ssh /etc/ssh-bootstrap \
    && sudo mkdir /etc/ssh

WORKDIR /gitlab-gdk/gitlab-development-kit

COPY --chmod=755 --chown=gdk:gdk ./install-config-gdk.sh /gitlab-gdk/gitlab-development-kit/install-config-gdk.sh

# Install and configure GDK
RUN ./install-config-gdk.sh

VOLUME /etc/ssh

EXPOSE 2022/tcp
EXPOSE 2222/tcp
EXPOSE 3000/tcp
EXPOSE 3005/tcp
EXPOSE 3010/tcp
EXPOSE 3038/tcp
EXPOSE 5100/tcp
EXPOSE 5778/tcp
EXPOSE 9000/tcp

CMD ["/home/<USER>/gdk-container-startup.sh"]
